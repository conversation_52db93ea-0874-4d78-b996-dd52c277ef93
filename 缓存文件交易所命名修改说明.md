# 股票缓存文件交易所命名修改说明

## 修改目标
将股票数据预缓存程序的缓存文件命名格式统一修改为：`股票代码_交易所代码.csv`

## 交易所代码规则

### 代码对应关系
- **XSHG**：上海证券交易所（Shanghai Stock Exchange）
- **XSHE**：深圳证券交易所（Shenzhen Stock Exchange）

### 股票代码与交易所映射
| 股票代码前缀 | 交易所代码 | 板块类型 | 文件名示例 |
|-------------|-----------|---------|-----------|
| 60 | XSHG | 上海主板 | 600001_XSHG.csv |
| 68 | XSHG | 科创板 | 688001_XSHG.csv |
| 00 | XSHE | 深圳主板 | 000001_XSHE.csv |
| 002 | XSHE | 创业板 | 002001_XSHE.csv |
| 003 | XSHE | 创业板 | 003001_XSHE.csv |
| 30 | XSHE | 创业板 | 300001_XSHE.csv |

## 主要修改内容

### 1. 新增交易所代码检测方法
```python
def _get_exchange_code(self, stock_code: str) -> str:
    """获取股票交易所代码"""
    normalized_code = self._normalize_stock_code(stock_code)
    
    if normalized_code.startswith('60') or normalized_code.startswith('68'):
        return "XSHG"  # 上海交易所
    elif (normalized_code.startswith('00') or 
          normalized_code.startswith('002') or 
          normalized_code.startswith('003') or 
          normalized_code.startswith('30')):
        return "XSHE"  # 深圳交易所
    else:
        return "XSHE"  # 默认深圳交易所
```

### 2. 修改文件路径生成方法
```python
def _get_cache_file_path(self, stock_code: str, use_exchange_suffix: bool = True) -> Path:
    """获取股票数据缓存文件路径"""
    if use_exchange_suffix:
        exchange_code = self._get_exchange_code(stock_code)
        filename_base = f"{stock_code}_{exchange_code}"
    else:
        filename_base = stock_code
    
    # 生成完整文件路径...
```

### 3. 增强缓存文件查找逻辑
- **优先查找新格式**：`股票代码_交易所代码.csv`
- **兼容旧格式**：如果新格式不存在，查找 `股票代码.csv`
- **自动标记迁移**：找到旧格式文件时记录日志

### 4. 修改保存缓存方法
- **统一使用新格式**：所有新保存的文件都使用交易所代码后缀
- **自动删除旧文件**：保存时检查并删除对应的旧格式文件
- **增强日志信息**：显示交易所代码和文件名

### 5. 更新清理缓存方法
- **双格式清理**：同时清理新旧两种格式的过期文件
- **完整性保证**：确保不遗留任何过期文件

## 兼容性处理

### 读取兼容性
1. **优先读取新格式**：`股票代码_交易所代码.csv`
2. **降级到旧格式**：如果新格式不存在，读取 `股票代码.csv`
3. **透明切换**：用户无感知的格式切换

### 自动迁移机制
1. **保存时迁移**：新保存数据时自动删除旧格式文件
2. **日志记录**：详细记录迁移操作
3. **错误处理**：迁移失败时不影响主要功能

## 辅助工具

### 1. 文件迁移工具 (`migrate_cache_to_exchange_format.py`)
**功能**：批量将现有的旧格式文件重命名为新格式

**使用方法**：
```bash
# 预览迁移操作（试运行）
python migrate_cache_to_exchange_format.py

# 执行实际迁移
python migrate_cache_to_exchange_format.py --execute

# 指定缓存目录
python migrate_cache_to_exchange_format.py --cache-dir custom_cache --execute

# 详细输出
python migrate_cache_to_exchange_format.py --execute --verbose
```

**特性**：
- 支持试运行模式，预览迁移操作
- 自动检测文件格式，跳过已迁移的文件
- 提供详细的迁移统计信息
- 完整的错误处理和日志记录

### 2. 命名逻辑测试工具 (`test_exchange_naming.py`)
**功能**：验证交易所代码检测和文件命名逻辑

**测试内容**：
- 交易所代码检测准确性
- 文件命名格式正确性
- 新旧格式兼容性

## 使用效果对比

### 修改前（不一致）
```
第一次运行：000001_主板.csv, 600001_主板.csv
第二次运行：000001.csv, 600001.csv
```

### 修改后（统一格式）
```
始终使用：000001_XSHE.csv, 600001_XSHG.csv
```

## 实际应用示例

### 常见股票的文件命名
```
平安银行 (000001) → 000001_XSHE.csv
万科A (000002)   → 000002_XSHE.csv
中兴通讯 (000063) → 000063_XSHE.csv
比亚迪 (002594)   → 002594_XSHE.csv
宁德时代 (300750) → 300750_XSHE.csv
浦发银行 (600000) → 600000_XSHG.csv
贵州茅台 (600519) → 600519_XSHG.csv
中芯国际 (688981) → 688981_XSHG.csv
```

## 注意事项

### 1. 数据安全
- **备份建议**：大规模迁移前建议备份缓存目录
- **渐进迁移**：程序会在使用过程中逐步迁移文件
- **错误恢复**：迁移失败不会影响原有数据

### 2. 性能影响
- **首次运行**：可能需要额外时间进行文件查找和迁移
- **后续运行**：性能与之前相同
- **存储空间**：迁移过程中可能短暂占用额外空间

### 3. 兼容性
- **向后兼容**：完全兼容现有的旧格式文件
- **向前兼容**：新格式文件可以被更新版本的程序识别
- **工具兼容**：其他依赖缓存文件的工具需要相应更新

## 验证方法

### 1. 运行测试程序
```bash
python test_exchange_naming.py
```

### 2. 检查现有缓存
```bash
# 查看缓存目录结构
ls -la stock_cache/data/*/

# 查找新格式文件
find stock_cache -name "*_XSHG.csv" -o -name "*_XSHE.csv"

# 查找旧格式文件
find stock_cache -name "*.csv" ! -name "*_XSHG.csv" ! -name "*_XSHE.csv"
```

### 3. 运行迁移工具
```bash
# 预览迁移
python migrate_cache_to_exchange_format.py

# 执行迁移
python migrate_cache_to_exchange_format.py --execute
```

## 总结

此次修改成功实现了：

✅ **统一命名格式**：所有缓存文件使用 `股票代码_交易所代码.csv` 格式
✅ **标准交易所代码**：使用国际标准的 XSHG 和 XSHE 代码
✅ **完全向后兼容**：现有旧格式文件仍可正常读取
✅ **自动迁移机制**：无需手动干预的文件格式升级
✅ **辅助工具支持**：提供迁移和测试工具
✅ **详细日志记录**：完整的操作追踪和错误处理

这样的命名格式更加标准化，便于与其他金融数据系统集成，同时保持了良好的可读性和维护性。
