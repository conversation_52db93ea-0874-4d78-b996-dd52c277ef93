#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试股票代码过滤功能
"""

import sys
import os
import tempfile

def test_stock_code_validation():
    """测试股票代码验证功能"""
    print("=== 测试股票代码验证功能 ===")
    
    try:
        # 导入验证函数
        sys.path.append('.')
        from stock_data_precacher import is_valid_a_stock_code
        
        # 测试用例
        test_cases = [
            # 有效的A股代码
            ("000001", True, "深圳主板"),
            ("000002", True, "深圳主板"),
            ("002001", True, "创业板"),
            ("003001", True, "创业板"),
            ("300001", True, "创业板"),
            ("600001", True, "上海主板"),
            ("688001", True, "科创板"),
            
            # 无效的代码（应被过滤）
            ("400001", False, "新三板"),
            ("430001", False, "北交所"),
            ("800001", False, "其他8开头"),
            ("900001", False, "B股"),
            
            # 格式无效的代码
            ("12345", False, "长度不足"),
            ("1234567", False, "长度过长"),
            ("abcdef", False, "非数字"),
            ("", False, "空字符串"),
            (None, False, "None值"),
        ]
        
        all_passed = True
        for code, expected, description in test_cases:
            try:
                actual = is_valid_a_stock_code(code) if code is not None else False
                status = "✓" if actual == expected else "✗"
                print(f"  {status} {code}: {actual} (期望: {expected}) - {description}")
                if actual != expected:
                    all_passed = False
            except Exception as e:
                print(f"  ✗ {code}: 测试异常 - {e}")
                all_passed = False
        
        return all_passed
        
    except ImportError as e:
        print(f"  ✗ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"  ✗ 测试失败: {e}")
        return False

def test_stock_code_filtering():
    """测试股票代码过滤功能"""
    print("\n=== 测试股票代码过滤功能 ===")
    
    try:
        from stock_data_precacher import filter_a_stock_codes
        
        # 测试股票代码列表
        test_codes = [
            "000001",  # 深圳主板
            "000002",  # 深圳主板
            "002001",  # 创业板
            "300001",  # 创业板
            "600001",  # 上海主板
            "688001",  # 科创板
            "400001",  # 新三板（应被过滤）
            "430001",  # 北交所（应被过滤）
            "800001",  # 其他8开头（应被过滤）
            "900001",  # B股（应被过滤）
            "invalid", # 无效格式（应被过滤）
        ]
        
        valid_codes, stats = filter_a_stock_codes(test_codes)
        
        print(f"  原始代码数量: {stats['total']}")
        print(f"  有效A股代码: {stats['valid']}")
        print(f"  过滤代码数量: {stats['filtered']}")
        print(f"  过滤详情:")
        print(f"    新三板(4开头): {stats['new_third_board']}")
        print(f"    北交所等(8开头): {stats['bse_or_other']}")
        print(f"    B股等(9开头): {stats['b_share_or_other']}")
        print(f"    格式无效: {stats['invalid_format']}")
        
        print(f"  有效代码列表: {valid_codes}")
        
        # 验证结果
        expected_valid = ["000001", "000002", "002001", "300001", "600001", "688001"]
        if set(valid_codes) == set(expected_valid):
            print("  ✓ 过滤结果正确")
            return True
        else:
            print("  ✗ 过滤结果不正确")
            print(f"    期望: {expected_valid}")
            print(f"    实际: {valid_codes}")
            return False
            
    except Exception as e:
        print(f"  ✗ 测试失败: {e}")
        return False

def test_cache_validation():
    """测试缓存管理器中的验证功能"""
    print("\n=== 测试缓存管理器验证功能 ===")
    
    try:
        from stock_data_cache import StockDataCache
        
        # 创建临时缓存管理器
        with tempfile.TemporaryDirectory() as temp_dir:
            cache = StockDataCache(cache_dir=temp_dir)
            
            # 测试有效和无效的股票代码
            test_cases = [
                ("000001", True),   # 有效A股
                ("600001", True),   # 有效A股
                ("400001", False),  # 新三板
                ("800001", False),  # 其他
                ("900001", False),  # B股
            ]
            
            all_passed = True
            for code, expected_valid in test_cases:
                is_valid = cache._is_valid_a_stock_code(code)
                exchange = cache._get_exchange_code(code)
                
                status = "✓" if is_valid == expected_valid else "✗"
                print(f"  {status} {code}: 有效={is_valid}, 交易所={exchange}")
                
                if is_valid != expected_valid:
                    all_passed = False
                
                # 对于无效代码，交易所应该返回 UNKNOWN
                if not expected_valid and exchange != "UNKNOWN":
                    print(f"    ⚠ 无效代码的交易所应为UNKNOWN，实际为{exchange}")
            
            return all_passed
            
    except Exception as e:
        print(f"  ✗ 测试失败: {e}")
        return False

def test_csv_loading_with_filter():
    """测试CSV加载时的过滤功能"""
    print("\n=== 测试CSV加载过滤功能 ===")
    
    try:
        from stock_data_precacher import load_stock_list_from_csv
        
        # 创建测试CSV文件
        test_data = """secID,name
000001,平安银行
000002,万科A
002001,新和成
300001,特锐德
600001,邮储银行
688001,华兴源创
400001,新三板股票
800001,其他股票
900001,B股股票
invalid,无效代码"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
            f.write(test_data)
            temp_file = f.name
        
        try:
            # 加载并过滤股票列表
            valid_codes = load_stock_list_from_csv(temp_file)
            
            print(f"  加载的有效股票代码: {valid_codes}")
            
            # 验证结果
            expected_codes = ["000001", "000002", "002001", "300001", "600001", "688001"]
            if set(valid_codes) == set(expected_codes):
                print("  ✓ CSV加载和过滤功能正常")
                return True
            else:
                print("  ✗ CSV加载和过滤结果不正确")
                return False
                
        finally:
            # 清理临时文件
            os.unlink(temp_file)
            
    except Exception as e:
        print(f"  ✗ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("股票代码过滤功能测试")
    print("=" * 60)
    
    results = []
    
    try:
        results.append(("股票代码验证", test_stock_code_validation()))
        results.append(("股票代码过滤", test_stock_code_filtering()))
        results.append(("缓存管理器验证", test_cache_validation()))
        results.append(("CSV加载过滤", test_csv_loading_with_filter()))
        
        # 汇总结果
        print("\n" + "=" * 60)
        print("测试结果汇总:")
        
        all_passed = True
        for test_name, passed in results:
            status = "✓ 通过" if passed else "✗ 失败"
            print(f"  {status} {test_name}")
            if not passed:
                all_passed = False
        
        print("\n" + "=" * 60)
        if all_passed:
            print("🎉 所有测试都通过了！")
            print("\n过滤规则总结:")
            print("✅ 保留的A股代码:")
            print("  - 00开头: 深圳主板")
            print("  - 002开头: 创业板")
            print("  - 003开头: 创业板")
            print("  - 30开头: 创业板")
            print("  - 60开头: 上海主板")
            print("  - 68开头: 科创板")
            
            print("\n❌ 过滤的代码:")
            print("  - 4开头: 新三板股票")
            print("  - 8开头: 北交所或其他非A股")
            print("  - 9开头: B股或其他非A股")
            print("  - 格式无效: 非6位数字等")
            
            print("\n功能特点:")
            print("- 在数据获取时自动过滤")
            print("- 详细的过滤统计信息")
            print("- 保持交易所命名格式")
            print("- 完整的日志记录")
        else:
            print("❌ 部分测试失败，请检查实现")
        
        return all_passed
        
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
