#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的创业板股票检查
"""

import json
import os
from pathlib import Path

def check_gem_cache():
    """检查创业板缓存"""
    print("检查创业板股票缓存情况")
    print("=" * 40)
    
    try:
        # 检查缓存索引
        cache_index_file = Path('stock_cache/metadata/cache_index.json')
        if cache_index_file.exists():
            with open(cache_index_file, 'r', encoding='utf-8') as f:
                cache_index = json.load(f)
            
            # 统计创业板股票
            gem_stocks = [code for code in cache_index.keys() if code.startswith('30')]
            
            print(f"总缓存股票数: {len(cache_index)}")
            print(f"创业板股票数: {len(gem_stocks)}")
            print(f"创业板股票列表: {gem_stocks}")
            
            # 检查特定股票
            test_stock = '300472.XSHE'
            if test_stock in cache_index:
                print(f"✅ {test_stock} 在缓存中")
            else:
                print(f"❌ {test_stock} 不在缓存中")
            
            return gem_stocks
        else:
            print("❌ 缓存索引文件不存在")
            return []
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return []

def check_cache_files():
    """检查缓存文件"""
    print("\n检查缓存文件结构")
    print("=" * 40)
    
    cache_dir = Path('stock_cache/data')
    if cache_dir.exists():
        subdirs = [d.name for d in cache_dir.iterdir() if d.is_dir()]
        print(f"缓存子目录: {sorted(subdirs)}")
        
        # 检查300目录
        gem_dir = cache_dir / '300'
        if gem_dir.exists():
            gem_files = list(gem_dir.glob('*.parquet'))
            print(f"300目录文件数: {len(gem_files)}")
            for file in gem_files:
                print(f"  {file.name}")
        else:
            print("❌ 300目录不存在")
    else:
        print("❌ 缓存数据目录不存在")

if __name__ == "__main__":
    gem_stocks = check_gem_cache()
    check_cache_files()
    
    print("\n结论:")
    if len(gem_stocks) < 5:
        print("⚠️ 创业板股票缓存数量过少")
        print("💡 建议运行 stock_data_precacher.py 补充创业板股票数据")
    else:
        print("✅ 创业板股票缓存充足")
