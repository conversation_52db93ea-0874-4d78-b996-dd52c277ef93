#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据预缓存程序网络连接修复工具
"""

import os
import sys
import subprocess
import requests
import urllib3

def clear_proxy_settings():
    """清除代理设置"""
    print("=== 清除代理设置 ===")
    
    # 环境变量中的代理设置
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY', 'all_proxy']
    cleared_count = 0
    
    for var in proxy_vars:
        if var in os.environ:
            print(f"  清除环境变量: {var} = {os.environ[var]}")
            del os.environ[var]
            cleared_count += 1
    
    if cleared_count == 0:
        print("  ✓ 未发现需要清除的代理设置")
    else:
        print(f"  ✓ 已清除 {cleared_count} 个代理设置")
    
    return cleared_count > 0

def disable_ssl_warnings():
    """禁用SSL警告"""
    print("\n=== 禁用SSL警告 ===")
    try:
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        print("  ✓ SSL警告已禁用")
        return True
    except Exception as e:
        print(f"  ✗ 禁用SSL警告失败: {e}")
        return False

def test_network_connectivity():
    """测试网络连接"""
    print("\n=== 测试网络连接 ===")
    
    test_urls = [
        "https://www.baidu.com",
        "https://push2his.eastmoney.com",
        "https://httpbin.org/get",
    ]
    
    session = requests.Session()
    session.proxies = {}  # 确保不使用代理
    session.verify = False  # 禁用SSL验证
    
    success_count = 0
    
    for url in test_urls:
        try:
            print(f"  测试 {url} ...", end="")
            response = session.get(url, timeout=10)
            if response.status_code == 200:
                print(" ✓ 连接成功")
                success_count += 1
            else:
                print(f" ⚠ 状态码: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f" ✗ 连接失败: {e}")
        except Exception as e:
            print(f" ✗ 未知错误: {e}")
    
    print(f"\n  连接成功率: {success_count}/{len(test_urls)} ({success_count/len(test_urls)*100:.1f}%)")
    return success_count > 0

def test_akshare_connection():
    """测试AKShare连接"""
    print("\n=== 测试AKShare连接 ===")
    
    try:
        print("  导入AKShare...", end="")
        import akshare as ak
        print(" ✓ 导入成功")
        
        print("  测试获取股票列表...", end="")
        stock_info = ak.stock_info_a_code_name()
        if len(stock_info) > 0:
            print(f" ✓ 成功获取 {len(stock_info)} 只股票")
            
            print("  测试下载单只股票数据...", end="")
            # 测试下载第一只股票的数据
            test_code = stock_info.iloc[0]['code']
            df = ak.stock_zh_a_hist(symbol=test_code, period="daily", 
                                  start_date="20241201", end_date="20241210")
            if df is not None and len(df) > 0:
                print(f" ✓ 成功下载 {len(df)} 条数据")
                return True
            else:
                print(" ✗ 下载数据为空")
                return False
        else:
            print(" ✗ 获取股票列表为空")
            return False
            
    except ImportError:
        print(" ✗ AKShare未安装")
        print("     请运行: pip install akshare")
        return False
    except Exception as e:
        print(f" ✗ AKShare连接失败: {e}")
        return False

def create_network_config_script():
    """创建网络配置脚本"""
    print("\n=== 创建网络配置脚本 ===")
    
    script_content = '''#!/bin/bash
# 股票数据预缓存程序网络配置脚本

echo "清除代理设置..."
unset HTTP_PROXY
unset HTTPS_PROXY
unset http_proxy
unset https_proxy
unset ALL_PROXY
unset all_proxy

echo "代理设置已清除"
echo "当前代理状态:"
echo "HTTP_PROXY: $HTTP_PROXY"
echo "HTTPS_PROXY: $HTTPS_PROXY"

echo "启动股票数据预缓存程序..."
python stock_data_precacher.py "$@"
'''
    
    try:
        with open('run_without_proxy.sh', 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        # 设置执行权限（在Unix系统上）
        try:
            os.chmod('run_without_proxy.sh', 0o755)
        except:
            pass  # Windows系统可能不支持chmod
        
        print("  ✓ 已创建 run_without_proxy.sh 脚本")
        print("    使用方法: ./run_without_proxy.sh")
        return True
        
    except Exception as e:
        print(f"  ✗ 创建脚本失败: {e}")
        return False

def create_python_wrapper():
    """创建Python包装脚本"""
    print("\n=== 创建Python包装脚本 ===")
    
    wrapper_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
无代理模式启动股票数据预缓存程序
"""

import os
import sys
import subprocess

def main():
    # 清除代理设置
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY', 'all_proxy']
    for var in proxy_vars:
        if var in os.environ:
            print(f"清除代理设置: {var}")
            del os.environ[var]
    
    print("代理设置已清除，启动预缓存程序...")
    
    # 启动预缓存程序
    try:
        cmd = [sys.executable, 'stock_data_precacher.py'] + sys.argv[1:]
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"程序执行失败: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\\n程序被用户中断")
        sys.exit(0)

if __name__ == "__main__":
    main()
'''
    
    try:
        with open('run_precacher_no_proxy.py', 'w', encoding='utf-8') as f:
            f.write(wrapper_content)
        
        print("  ✓ 已创建 run_precacher_no_proxy.py 脚本")
        print("    使用方法: python run_precacher_no_proxy.py")
        return True
        
    except Exception as e:
        print(f"  ✗ 创建Python包装脚本失败: {e}")
        return False

def provide_manual_solutions():
    """提供手动解决方案"""
    print("\n" + "=" * 60)
    print("手动解决方案")
    print("=" * 60)
    
    print("\n1. 临时禁用代理（Windows PowerShell）:")
    print("   $env:HTTP_PROXY = $null")
    print("   $env:HTTPS_PROXY = $null")
    print("   $env:http_proxy = $null")
    print("   $env:https_proxy = $null")
    
    print("\n2. 临时禁用代理（Windows CMD）:")
    print("   set HTTP_PROXY=")
    print("   set HTTPS_PROXY=")
    print("   set http_proxy=")
    print("   set https_proxy=")
    
    print("\n3. 临时禁用代理（Linux/Mac）:")
    print("   unset HTTP_PROXY HTTPS_PROXY http_proxy https_proxy")
    
    print("\n4. 在Python代码中禁用代理:")
    print("   import os")
    print("   for key in ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']:")
    print("       os.environ.pop(key, None)")
    
    print("\n5. 检查网络连接:")
    print("   - 确认可以访问 https://www.baidu.com")
    print("   - 确认可以访问 https://push2his.eastmoney.com")
    print("   - 检查防火墙设置")
    print("   - 尝试更换网络环境")

def main():
    """主函数"""
    print("股票数据预缓存程序网络连接修复工具")
    print("=" * 60)
    
    # 执行修复步骤
    proxy_cleared = clear_proxy_settings()
    ssl_disabled = disable_ssl_warnings()
    network_ok = test_network_connectivity()
    akshare_ok = test_akshare_connection()
    
    # 创建辅助脚本
    script_created = create_network_config_script()
    wrapper_created = create_python_wrapper()
    
    # 总结结果
    print("\n" + "=" * 60)
    print("修复结果总结")
    print("=" * 60)
    
    if proxy_cleared:
        print("✅ 已清除代理设置")
    else:
        print("ℹ️  未发现代理设置")
    
    if ssl_disabled:
        print("✅ SSL警告已禁用")
    
    if network_ok:
        print("✅ 网络连接正常")
    else:
        print("❌ 网络连接异常")
    
    if akshare_ok:
        print("✅ AKShare连接正常")
        print("🎉 问题已解决，可以运行预缓存程序了！")
    else:
        print("❌ AKShare连接异常")
        print("⚠️  需要进一步排查问题")
    
    if script_created and wrapper_created:
        print("✅ 辅助脚本已创建")
    
    # 提供解决方案
    provide_manual_solutions()
    
    print(f"\n推荐使用方式:")
    print(f"  python run_precacher_no_proxy.py")

if __name__ == "__main__":
    main()
