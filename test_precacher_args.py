#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试股票数据预缓存程序的参数解析
"""

import sys
import argparse

def test_args():
    """测试参数解析逻辑"""
    
    # 模拟原始的参数解析逻辑
    parser = argparse.ArgumentParser(
        description="股票数据预缓存程序（默认下载所有A股数据）",
        epilog="注意：程序默认启用 --all 模式下载所有A股数据。如需使用CSV文件，请指定 --input 参数；如需禁用全部下载，请使用 --no-all 参数。"
    )
    parser.add_argument("--input", "-i", type=str, help="输入CSV文件路径（包含股票代码），指定后将覆盖默认的全部A股模式")
    parser.add_argument("--all", "-a", action="store_true", default=True, help="下载所有A股数据（默认启用）")
    parser.add_argument("--no-all", action="store_true", help="禁用默认的全部A股下载模式，改为交互式选择CSV文件")
    parser.add_argument("--cache-dir", "-c", type=str, default="stock_cache", help="缓存目录路径")
    parser.add_argument("--workers", "-w", type=int, default=5, help="并发下载线程数")
    parser.add_argument("--days", "-d", type=int, default=365, help="下载天数（默认365天）")
    parser.add_argument("--force", "-f", action="store_true", help="强制刷新所有缓存")
    parser.add_argument("--status", "-s", action="store_true", help="显示缓存状态")
    parser.add_argument("--clean", action="store_true", help="清理过期缓存")
    
    # 测试不同的参数组合
    test_cases = [
        [],  # 默认情况
        ["--help"],  # 帮助信息
        ["--input", "test.csv"],  # 指定输入文件
        ["--no-all"],  # 禁用全部模式
        ["--all"],  # 显式启用全部模式
        ["--status"],  # 查看状态
    ]
    
    for i, test_args in enumerate(test_cases):
        print(f"\n=== 测试用例 {i+1}: {' '.join(test_args) if test_args else '(无参数)'} ===")
        
        try:
            if "--help" in test_args:
                # 特殊处理帮助信息
                parser.print_help()
                continue
                
            args = parser.parse_args(test_args)
            
            # 处理 --no-all 参数
            if hasattr(args, 'no_all') and args.no_all:
                args.all = False
            
            print(f"args.input: {args.input}")
            print(f"args.all: {args.all}")
            print(f"args.no_all: {getattr(args, 'no_all', False)}")
            print(f"args.status: {args.status}")
            
            # 模拟股票列表确定逻辑
            if args.status:
                print("→ 将显示缓存状态")
            elif args.input:
                print(f"→ 将使用输入文件: {args.input}")
            elif args.all:
                print("→ 将下载所有A股数据（默认模式）")
            else:
                print("→ 将进入交互式CSV文件选择模式")
                
        except SystemExit:
            # argparse 在遇到 --help 或错误时会调用 sys.exit()
            pass
        except Exception as e:
            print(f"错误: {e}")

if __name__ == "__main__":
    test_args()
