#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试股票缓存文件交易所命名逻辑
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

def test_exchange_code_detection():
    """测试交易所代码检测功能"""
    print("=== 测试交易所代码检测 ===")
    
    def get_exchange_code(stock_code: str) -> str:
        """获取股票交易所代码"""
        # 标准化股票代码
        if '.' in stock_code:
            normalized_code = stock_code.split('.')[0]
        else:
            normalized_code = stock_code
        
        # 根据股票代码判断交易所
        if normalized_code.startswith('60') or normalized_code.startswith('68'):
            return "XSHG"
        elif (normalized_code.startswith('00') or 
              normalized_code.startswith('002') or 
              normalized_code.startswith('003') or 
              normalized_code.startswith('30')):
            return "XSHE"
        else:
            return "XSHE"
    
    # 测试用例
    test_cases = [
        ("000001", "XSHE"),      # 深圳主板
        ("000002", "XSHE"),      # 深圳主板
        ("002001", "XSHE"),      # 创业板
        ("003001", "XSHE"),      # 创业板
        ("300001", "XSHE"),      # 创业板
        ("600001", "XSHG"),      # 上海主板
        ("600036", "XSHG"),      # 上海主板
        ("688001", "XSHG"),      # 科创板
        ("688088", "XSHG"),      # 科创板
    ]
    
    all_passed = True
    for stock_code, expected_exchange in test_cases:
        actual_exchange = get_exchange_code(stock_code)
        passed = actual_exchange == expected_exchange
        status = "✓" if passed else "✗"
        print(f"  {status} {stock_code}: {actual_exchange} (期望: {expected_exchange})")
        if not passed:
            all_passed = False
    
    return all_passed

def test_file_naming():
    """测试文件命名逻辑"""
    print("\n=== 测试文件命名逻辑 ===")
    
    def get_exchange_code(stock_code: str) -> str:
        """获取股票交易所代码"""
        if '.' in stock_code:
            normalized_code = stock_code.split('.')[0]
        else:
            normalized_code = stock_code
        
        if normalized_code.startswith('60') or normalized_code.startswith('68'):
            return "XSHG"
        elif (normalized_code.startswith('00') or 
              normalized_code.startswith('002') or 
              normalized_code.startswith('003') or 
              normalized_code.startswith('30')):
            return "XSHE"
        else:
            return "XSHE"
    
    # 测试文件命名
    test_stocks = ["000001", "002001", "300001", "600001", "688001"]
    
    for stock_code in test_stocks:
        exchange_code = get_exchange_code(stock_code)
        new_filename = f"{stock_code}_{exchange_code}.csv"
        old_filename = f"{stock_code}.csv"
        
        print(f"  {stock_code} ({exchange_code}):")
        print(f"    新格式: {new_filename}")
        print(f"    旧格式: {old_filename}")
    
    return True

def test_compatibility():
    """测试新旧格式兼容性"""
    print("\n=== 测试新旧格式兼容性 ===")
    
    try:
        # 导入缓存管理器
        from stock_data_cache import StockDataCache
        
        # 创建临时缓存管理器
        with tempfile.TemporaryDirectory() as temp_dir:
            cache = StockDataCache(cache_dir=temp_dir)
            
            # 创建测试数据
            import pandas as pd
            from datetime import datetime, timedelta
            
            test_stock = "000001"
            
            # 创建测试数据
            dates = pd.date_range(start=datetime.now() - timedelta(days=30), end=datetime.now(), freq='D')
            test_data = pd.DataFrame({
                'date': dates,
                'open': [10.0] * len(dates),
                'close': [10.5] * len(dates),
                'high': [11.0] * len(dates),
                'low': [9.5] * len(dates),
                'volume': [1000000] * len(dates),
                'stock_code': [test_stock] * len(dates)
            })
            
            # 1. 创建旧格式文件
            old_file_path = cache._get_cache_file_path(test_stock, use_exchange_suffix=False)
            old_file_path.parent.mkdir(parents=True, exist_ok=True)
            test_data.to_csv(old_file_path, index=False, encoding='utf-8')
            print(f"  创建旧格式文件: {old_file_path}")
            
            # 2. 更新缓存索引
            cache.cache_index[test_stock] = {
                'last_update': datetime.now().isoformat(),
                'record_count': len(test_data),
                'date_range': {
                    'start': test_data['date'].min().isoformat(),
                    'end': test_data['date'].max().isoformat()
                }
            }
            cache._save_cache_index()
            
            # 3. 测试读取旧格式文件
            print("  测试读取旧格式文件...")
            df = cache.get_stock_data(test_stock)
            if df is not None and len(df) > 0:
                print(f"    ✓ 成功读取旧格式文件，数据行数: {len(df)}")
            else:
                print("    ✗ 读取旧格式文件失败")
                return False
            
            # 4. 测试保存新格式文件（应该会迁移）
            print("  测试保存新格式文件...")
            cache._save_to_cache(test_stock, test_data)
            
            # 5. 检查新格式文件是否创建
            new_file_path = cache._get_cache_file_path(test_stock, use_exchange_suffix=True)
            if new_file_path.exists():
                print(f"    ✓ 新格式文件已创建: {new_file_path.name}")
            else:
                print("    ✗ 新格式文件创建失败")
                return False
            
            # 6. 检查旧格式文件是否被删除
            if not old_file_path.exists():
                print("    ✓ 旧格式文件已删除")
            else:
                print("    ✗ 旧格式文件未删除")
                return False
            
            return True
            
    except ImportError:
        print("    ✗ 无法导入 stock_data_cache 模块")
        return False
    except Exception as e:
        print(f"    ✗ 测试过程中发生错误: {e}")
        return False

def main():
    """主函数"""
    print("股票缓存文件交易所命名逻辑测试")
    print("=" * 50)
    
    results = []
    
    try:
        results.append(("交易所代码检测", test_exchange_code_detection()))
        results.append(("文件命名逻辑", test_file_naming()))
        results.append(("新旧格式兼容性", test_compatibility()))
        
        # 汇总结果
        print("\n" + "=" * 50)
        print("测试结果汇总:")
        
        all_passed = True
        for test_name, passed in results:
            status = "✓ 通过" if passed else "✗ 失败"
            print(f"  {status} {test_name}")
            if not passed:
                all_passed = False
        
        print("\n" + "=" * 50)
        if all_passed:
            print("🎉 所有测试都通过了！")
            print("\n修改总结:")
            print("1. ✅ 新增了交易所代码检测功能")
            print("2. ✅ 修改了文件命名逻辑，统一使用 '股票代码_交易所代码.csv' 格式")
            print("3. ✅ 增加了新旧格式的兼容性支持")
            print("4. ✅ 实现了自动迁移机制")
            
            print("\n交易所代码规则:")
            print("- XSHG: 上海证券交易所（60开头的主板 + 68开头的科创板）")
            print("- XSHE: 深圳证券交易所（00开头的主板 + 002/003/30开头的创业板）")
            
            print("\n下一步:")
            print("- 可以运行 python migrate_cache_to_exchange_format.py 来迁移现有文件")
            print("- 运行 python stock_data_precacher.py 测试新的命名逻辑")
        else:
            print("❌ 部分测试失败，请检查修改内容")
        
        return all_passed
        
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
