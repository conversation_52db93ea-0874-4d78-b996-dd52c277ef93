#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据预缓存程序网络诊断工具
"""

import os
import sys
import socket
import requests
import urllib3
from urllib.parse import urlparse
import ssl
import time

def check_proxy_settings():
    """检查代理设置"""
    print("=== 检查代理设置 ===")
    
    # 检查环境变量中的代理设置
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY', 'all_proxy']
    proxy_found = False
    
    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            print(f"  发现代理设置: {var} = {value}")
            proxy_found = True
    
    if not proxy_found:
        print("  ✓ 环境变量中未发现代理设置")
    
    # 检查requests库的代理设置
    try:
        session = requests.Session()
        proxies = session.proxies
        if proxies:
            print(f"  requests代理设置: {proxies}")
        else:
            print("  ✓ requests库未配置代理")
    except Exception as e:
        print(f"  ⚠ 检查requests代理时出错: {e}")
    
    return proxy_found

def test_direct_connection():
    """测试直接网络连接"""
    print("\n=== 测试直接网络连接 ===")
    
    test_hosts = [
        ("push2his.eastmoney.com", 443),
        ("www.baidu.com", 80),
        ("www.google.com", 80),
        ("*******", 53),
    ]
    
    results = {}
    
    for host, port in test_hosts:
        try:
            print(f"  测试连接 {host}:{port} ...", end="")
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(" ✓ 连接成功")
                results[host] = True
            else:
                print(f" ✗ 连接失败 (错误码: {result})")
                results[host] = False
        except Exception as e:
            print(f" ✗ 连接异常: {e}")
            results[host] = False
    
    return results

def test_https_connection():
    """测试HTTPS连接"""
    print("\n=== 测试HTTPS连接 ===")
    
    test_urls = [
        "https://push2his.eastmoney.com",
        "https://www.baidu.com",
        "https://httpbin.org/get",
    ]
    
    # 禁用SSL警告
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    
    for url in test_urls:
        print(f"  测试 {url} ...", end="")
        try:
            # 创建一个不使用代理的session
            session = requests.Session()
            session.proxies = {}  # 清空代理设置
            
            response = session.get(url, timeout=10, verify=False)
            print(f" ✓ 状态码: {response.status_code}")
        except requests.exceptions.ProxyError as e:
            print(f" ✗ 代理错误: {e}")
        except requests.exceptions.ConnectionError as e:
            print(f" ✗ 连接错误: {e}")
        except requests.exceptions.Timeout as e:
            print(f" ✗ 超时错误: {e}")
        except Exception as e:
            print(f" ✗ 其他错误: {e}")

def test_akshare_connection():
    """测试AKShare连接"""
    print("\n=== 测试AKShare连接 ===")
    
    try:
        print("  导入AKShare库...", end="")
        import akshare as ak
        print(" ✓ 导入成功")
        
        print("  测试获取股票基本信息...", end="")
        # 设置不使用代理
        original_proxies = os.environ.copy()
        for key in ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']:
            if key in os.environ:
                del os.environ[key]
        
        try:
            # 尝试获取简单的股票信息
            stock_info = ak.stock_info_a_code_name()
            if len(stock_info) > 0:
                print(f" ✓ 成功获取 {len(stock_info)} 只股票信息")
                return True
            else:
                print(" ✗ 获取的股票信息为空")
                return False
        finally:
            # 恢复原始环境变量
            os.environ.clear()
            os.environ.update(original_proxies)
            
    except ImportError:
        print(" ✗ AKShare库未安装")
        return False
    except Exception as e:
        print(f" ✗ AKShare连接失败: {e}")
        return False

def test_stock_data_download():
    """测试股票数据下载"""
    print("\n=== 测试股票数据下载 ===")
    
    try:
        import akshare as ak
        
        # 清除代理设置
        session = requests.Session()
        session.proxies = {}
        
        # 临时清除环境变量中的代理
        proxy_backup = {}
        proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
        for var in proxy_vars:
            if var in os.environ:
                proxy_backup[var] = os.environ[var]
                del os.environ[var]
        
        try:
            print("  测试下载股票000001数据...", end="")
            
            # 尝试下载股票数据
            df = ak.stock_zh_a_hist(symbol="000001", period="daily", start_date="20241201", end_date="20241210")
            
            if df is not None and len(df) > 0:
                print(f" ✓ 成功下载 {len(df)} 条数据")
                print(f"    数据列: {list(df.columns)}")
                return True
            else:
                print(" ✗ 下载的数据为空")
                return False
                
        finally:
            # 恢复代理设置
            for var, value in proxy_backup.items():
                os.environ[var] = value
                
    except Exception as e:
        print(f" ✗ 下载失败: {e}")
        return False

def provide_solutions():
    """提供解决方案"""
    print("\n" + "=" * 60)
    print("解决方案建议")
    print("=" * 60)
    
    print("\n1. 禁用代理设置:")
    print("   # 临时禁用（当前会话）")
    print("   unset HTTP_PROXY HTTPS_PROXY http_proxy https_proxy")
    print("   ")
    print("   # 或在Python代码中禁用")
    print("   import os")
    print("   for key in ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']:")
    print("       os.environ.pop(key, None)")
    
    print("\n2. 配置requests不使用代理:")
    print("   import requests")
    print("   session = requests.Session()")
    print("   session.proxies = {}  # 清空代理设置")
    
    print("\n3. 检查防火墙和网络设置:")
    print("   - 确认防火墙允许Python程序访问网络")
    print("   - 检查公司网络是否有访问限制")
    print("   - 尝试使用VPN或更换网络环境")
    
    print("\n4. 修改股票数据缓存程序:")
    print("   - 在程序开始时清除代理设置")
    print("   - 增加网络连接重试机制")
    print("   - 添加连接超时和错误处理")

def main():
    """主函数"""
    print("股票数据预缓存程序网络诊断工具")
    print("=" * 60)
    
    # 执行诊断
    proxy_found = check_proxy_settings()
    connection_results = test_direct_connection()
    test_https_connection()
    akshare_ok = test_akshare_connection()
    stock_download_ok = test_stock_data_download()
    
    # 分析结果
    print("\n" + "=" * 60)
    print("诊断结果分析")
    print("=" * 60)
    
    if proxy_found:
        print("❌ 发现代理配置，这可能是连接问题的根本原因")
        print("   建议：禁用代理设置或正确配置代理")
    else:
        print("✅ 未发现代理配置问题")
    
    if connection_results.get("push2his.eastmoney.com", False):
        print("✅ 可以直接连接到东方财富服务器")
    else:
        print("❌ 无法连接到东方财富服务器")
        print("   建议：检查网络连接和防火墙设置")
    
    if akshare_ok:
        print("✅ AKShare库连接正常")
    else:
        print("❌ AKShare库连接异常")
        print("   建议：检查AKShare库版本和网络设置")
    
    if stock_download_ok:
        print("✅ 股票数据下载测试成功")
        print("   问题可能已解决，可以尝试重新运行预缓存程序")
    else:
        print("❌ 股票数据下载测试失败")
        print("   需要进一步排查网络问题")
    
    # 提供解决方案
    provide_solutions()

if __name__ == "__main__":
    main()
