#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试缓存系统
"""

import os
import sys

def test_imports():
    """测试导入"""
    try:
        print("测试基础库导入...")
        import pandas as pd
        print("✅ pandas 导入成功")
        
        import numpy as np
        print("✅ numpy 导入成功")
        
        try:
            import pyarrow
            print("✅ pyarrow 导入成功")
        except ImportError:
            print("⚠️ pyarrow 未安装，将使用CSV格式")
        
        print("\n测试项目模块导入...")
        from stock_data_cache import StockDataCache
        print("✅ stock_data_cache 导入成功")
        
        from standalone_stock_scorer import StockScorer, RealDataService
        print("✅ standalone_stock_scorer 导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cache_init():
    """测试缓存初始化"""
    try:
        print("\n测试缓存系统初始化...")
        from stock_data_cache import StockDataCache
        
        cache = StockDataCache(cache_dir='stock_cache')
        print("✅ 缓存系统初始化成功")
        
        status = cache.get_cache_status()
        print(f"✅ 缓存状态获取成功: {status['total_stocks']} 只股票")
        
        return True
        
    except Exception as e:
        print(f"❌ 缓存初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_scorer_init():
    """测试评分器初始化"""
    try:
        print("\n测试评分器初始化...")
        from standalone_stock_scorer import StockScorer
        
        scorer = StockScorer(cache_only=True, cache_dir='stock_cache')
        print("✅ 评分器初始化成功（纯缓存模式）")
        
        return True
        
    except Exception as e:
        print(f"❌ 评分器初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("简单测试 - 独立股票评分程序")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        sys.exit(1)
    
    # 测试缓存初始化
    if not test_cache_init():
        sys.exit(1)
    
    # 测试评分器初始化
    if not test_scorer_init():
        sys.exit(1)
    
    print("\n🎉 所有测试通过！")
    print("✅ 独立股票评分程序可以正常使用纯缓存模式")
