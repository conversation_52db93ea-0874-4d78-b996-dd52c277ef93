#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存系统测试脚本
"""

import sys
import os
sys.path.append('.')

from stock_data_cache import StockDataCache
import pandas as pd

def test_cache_system():
    """测试缓存系统"""
    print("=" * 60)
    print("缓存系统测试")
    print("=" * 60)
    
    try:
        # 初始化缓存系统
        cache = StockDataCache(cache_dir='stock_cache')
        print("✅ 缓存系统初始化成功")
        
        # 获取缓存状态
        status = cache.get_cache_status()
        print('\n📊 缓存状态:')
        for key, value in status.items():
            print(f'  {key}: {value}')
        
        # 测试读取一个缓存文件
        test_code = '000002'
        print(f'\n🔍 测试读取股票 {test_code} 的缓存数据...')
        
        data = cache.get_stock_data(test_code, start_date='2024-01-01', end_date='2025-06-30')
        if data is not None:
            print(f'✅ 成功从缓存读取股票 {test_code} 数据:')
            print(f'  数据条数: {len(data)}')
            print(f'  日期范围: {data["date"].min()} 到 {data["date"].max()}')
            print(f'  列名: {list(data.columns)}')
            print(f'  最新几行数据:')
            print(data.tail(3)[['date', 'open', 'high', 'low', 'close', 'volume']])
        else:
            print(f'❌ 未能读取股票 {test_code} 的缓存数据')
            
        return True
        
    except Exception as e:
        print(f"❌ 缓存系统测试失败: {e}")
        return False

if __name__ == "__main__":
    test_cache_system()
