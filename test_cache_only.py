#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试独立股票评分程序的纯缓存模式
"""

import pandas as pd
import os
import sys
from datetime import datetime

def create_test_data():
    """创建测试数据文件"""
    test_data = {
        'secID': ['000002', '000004', '000006'],
        'secShortName': ['万科A', '国华网安', '深振业A'],
        'closePrice': [8.50, 15.20, 6.80]
    }
    
    df = pd.DataFrame(test_data)
    test_file = 'test_stocks.csv'
    df.to_csv(test_file, index=False, encoding='utf-8-sig')
    print(f"✅ 创建测试数据文件: {test_file}")
    return test_file

def test_cache_only_mode():
    """测试纯缓存模式"""
    print("=" * 60)
    print("测试独立股票评分程序 - 纯缓存模式")
    print("=" * 60)
    
    # 创建测试数据
    test_file = create_test_data()
    
    try:
        # 导入评分程序
        from standalone_stock_scorer import StockScorer
        
        print("\n🔍 初始化评分器（纯缓存模式）...")
        scorer = StockScorer(cache_only=True, cache_dir='stock_cache')
        
        print("\n📊 测试单只股票评分...")
        
        # 创建测试数据
        test_stock_data = pd.DataFrame([{
            'secID': '000002',
            'secShortName': '万科A',
            'closePrice': 8.50
        }])
        
        # 测试评分
        result = scorer.score_single_stock(test_stock_data, '000002', '万科A')
        
        if result:
            if result.get('skipped', False):
                print(f"⚠️ 股票被跳过: {result.get('skip_reason', '未知原因')}")
            else:
                print(f"✅ 评分成功:")
                print(f"  股票代码: {result['stock_code']}")
                print(f"  股票名称: {result['stock_name']}")
                print(f"  总评分: {result.get('total_score', 'N/A')}")
                print(f"  评级: {result.get('grade', 'N/A')}")
                print(f"  数据点数: {result.get('data_points', 'N/A')}")
                print(f"  数据源: {result.get('data_source', 'N/A')}")
        else:
            print("❌ 评分失败")
            
        print("\n🧪 测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"🧹 清理测试文件: {test_file}")

def check_cache_status():
    """检查缓存状态"""
    try:
        from stock_data_cache import StockDataCache
        
        cache = StockDataCache(cache_dir='stock_cache')
        status = cache.get_cache_status()
        
        print("\n📊 缓存状态:")
        print(f"  总股票数: {status['total_stocks']}")
        print(f"  有效股票数: {status['valid_stocks']}")
        print(f"  过期股票数: {status['expired_stocks']}")
        print(f"  缓存大小: {status['cache_size_mb']} MB")
        print(f"  缓存目录: {status['cache_dir']}")
        
        return status['valid_stocks'] > 0
        
    except Exception as e:
        print(f"❌ 检查缓存状态失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试独立股票评分程序的纯缓存模式")
    
    # 检查缓存状态
    has_cache = check_cache_status()
    
    if not has_cache:
        print("\n⚠️ 警告: 缓存中没有有效数据")
        print("请先运行 stock_data_precacher.py 预缓存数据")
    else:
        print("\n✅ 缓存数据可用，开始测试")
        test_cache_only_mode()
