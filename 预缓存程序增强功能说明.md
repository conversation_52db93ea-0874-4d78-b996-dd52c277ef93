# 股票数据预缓存程序增强功能说明

## 新增功能概述

为股票数据预缓存程序 `stock_data_precacher.py` 添加了两个重要的增强功能：

1. **指数退避重试机制** - 提高下载成功率和稳定性
2. **智能跳过已下载股票** - 支持断点续传，提高效率

## 功能1：指数退避重试机制

### 功能描述
为每只股票的数据下载实现智能重试机制，采用指数退避策略处理网络波动和API限制。

### 技术规格
- **重试次数**：最多10次
- **等待策略**：指数退避（每次等待时间翻倍）
- **基础等待时间**：10秒
- **等待时间序列**：10秒 → 20秒 → 40秒 → 80秒 → 160秒 → 320秒 → 640秒 → 1280秒 → 2560秒

### 实现细节

#### 等待时间计算公式
```python
wait_time = base_delay * (2 ** attempt)
# base_delay = 10秒
# attempt = 0, 1, 2, 3, ...
```

#### 重试流程
```
第1次尝试 → 失败 → 等待10秒 → 第2次尝试
第2次尝试 → 失败 → 等待20秒 → 第3次尝试
第3次尝试 → 失败 → 等待40秒 → 第4次尝试
...
第10次尝试 → 失败 → 放弃，记录到失败列表
```

#### 日志记录
```
开始下载股票 000001 数据
重试下载股票 000001 数据 (第 1 次重试，共 9 次)
等待 10 秒后重试股票 000001...
重试下载股票 000001 数据 (第 2 次重试，共 9 次)
等待 20 秒后重试股票 000001...
✅ 股票 000001 重试成功，共 245 条记录
```

### 优势
- **提高成功率**：网络波动时自动重试
- **避免API限制**：指数退避减少请求频率
- **智能等待**：等待时间逐渐增加，适应不同类型的错误
- **详细日志**：清楚记录每次重试的状态

## 功能2：智能跳过已下载股票

### 功能描述
在开始下载前检查缓存文件的存在性和有效性，自动跳过已下载的股票，支持断点续传。

### 检查机制

#### 文件存在性检查
1. 优先检查新格式文件：`股票代码_交易所代码.csv`
2. 降级检查旧格式文件：`股票代码.csv`
3. 支持CSV和Parquet两种格式

#### 文件有效性验证
```python
def _validate_cache_file_content(self, cache_file: Path) -> bool:
    # 1. 检查文件大小（至少100字节）
    # 2. 尝试读取文件内容
    # 3. 验证必要列存在：date, open, close, high, low
    # 4. 检查是否有有效数据行
```

#### 验证标准
- **文件大小**：至少100字节（排除空文件或损坏文件）
- **必要列**：必须包含 `date`, `open`, `close`, `high`, `low` 列
- **数据完整性**：至少有一行有效数据（非空值）
- **格式正确性**：能够正常解析为DataFrame

### 跳过逻辑

#### 处理流程
```python
原始股票列表 → 检查缓存状态 → 分类处理
├── 已缓存且有效 → 跳过（标记为成功）
└── 未缓存或无效 → 加入下载队列
```

#### 日志输出
```
正在检查 5000 只股票的缓存状态...
📁 跳过 3200 只已缓存的股票（缓存已存在且有效）
🚀 开始下载 1800 只股票数据（总计 5000 只，跳过 3200 只）
```

### 断点续传支持
- **程序中断恢复**：重新运行时自动跳过已下载的股票
- **增量更新**：只下载新增或失效的股票数据
- **效率提升**：避免重复下载，节省时间和API调用

## 增强的进度报告

### 详细统计信息
```
🎉 批量下载完成:
   总股票数: 5000
   跳过缓存: 3200
   实际下载: 1800
   下载成功: 1750
   下载失败: 50
   总成功率: 4950/5000 (99.0%)
```

### 实时进度显示
```
✅ [1/1800] 000001 (XSHE) 下载成功
✅ [2/1800] 000002 (XSHE) 下载成功
...
📊 下载进度: 50/1800 (2.8%)
...
📊 下载进度: 1800/1800 (100.0%)
```

### 失败股票追踪
```
失败股票列表: 000123, 000456, 000789 等50只
```

## 兼容性保证

### 与现有功能兼容
- ✅ 交易所命名格式（XSHG/XSHE）
- ✅ 新旧文件格式兼容
- ✅ 强制刷新模式（`--force`）
- ✅ 并发下载机制
- ✅ 缓存索引管理

### 配置参数
```python
# 重试配置
max_retries = 10        # 最大重试次数
base_delay = 10         # 基础等待时间（秒）

# 文件验证配置
min_file_size = 100     # 最小文件大小（字节）
required_columns = ['date', 'open', 'close', 'high', 'low']
```

## 使用场景

### 场景1：首次全量下载
```bash
python stock_data_precacher.py
# 下载所有A股数据，每只股票最多重试10次
```

### 场景2：断点续传
```bash
# 程序中断后重新运行
python stock_data_precacher.py
# 自动跳过已下载的股票，只下载剩余部分
```

### 场景3：增量更新
```bash
# 定期运行，只更新新增或过期的数据
python stock_data_precacher.py
# 智能跳过有效缓存，只下载需要更新的股票
```

### 场景4：强制全量刷新
```bash
python stock_data_precacher.py --force
# 忽略缓存，强制重新下载所有数据
```

## 性能优化

### 下载效率提升
- **减少API调用**：跳过已缓存的股票
- **智能重试**：只对失败的股票进行重试
- **并发处理**：保持多线程下载能力

### 网络稳定性
- **指数退避**：适应网络波动和API限制
- **错误恢复**：自动处理临时性错误
- **资源保护**：避免过度请求导致的封禁

### 存储优化
- **文件验证**：确保缓存文件质量
- **格式兼容**：支持多种文件格式
- **空间节省**：避免重复下载相同数据

## 监控和调试

### 日志级别
- **INFO**：正常操作和进度信息
- **WARNING**：重试和跳过信息
- **ERROR**：失败和异常信息
- **DEBUG**：详细的调试信息

### 关键指标
- **跳过率**：已缓存股票的比例
- **成功率**：下载成功的比例
- **重试率**：需要重试的股票比例
- **平均重试次数**：每只股票的平均重试次数

## 总结

这两个增强功能显著提升了股票数据预缓存程序的：

1. **可靠性**：指数退避重试机制提高下载成功率
2. **效率性**：智能跳过减少不必要的下载
3. **实用性**：支持断点续传和增量更新
4. **可观测性**：详细的进度报告和状态信息

特别适合处理全体A股数据（5000+只股票）这样的大规模下载任务，能够有效应对网络波动、API限制等实际问题。
