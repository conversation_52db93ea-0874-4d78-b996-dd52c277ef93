#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断创业板股票缓存问题
"""

import json
import os
from pathlib import Path
import pandas as pd

def analyze_cache_coverage():
    """分析缓存覆盖范围"""
    print("=" * 60)
    print("创业板股票缓存覆盖范围分析")
    print("=" * 60)
    
    try:
        # 读取缓存索引
        cache_index_file = Path('stock_cache/metadata/cache_index.json')
        if not cache_index_file.exists():
            print("❌ 缓存索引文件不存在")
            return
        
        with open(cache_index_file, 'r', encoding='utf-8') as f:
            cache_index = json.load(f)
        
        print(f"📊 缓存索引总股票数: {len(cache_index)}")
        
        # 分析不同板块的股票数量
        main_board = []  # 主板 000xxx, 001xxx
        sme_board = []   # 中小板 002xxx
        gem_board = []   # 创业板 300xxx, 301xxx, 302xxx
        sh_main = []     # 上交所主板 600xxx, 601xxx
        sh_star = []     # 科创板 688xxx
        sh_new = []      # 上交所新股 603xxx, 605xxx
        
        for stock_code in cache_index.keys():
            if stock_code.startswith('000') or stock_code.startswith('001'):
                main_board.append(stock_code)
            elif stock_code.startswith('002'):
                sme_board.append(stock_code)
            elif stock_code.startswith('30'):
                gem_board.append(stock_code)
            elif stock_code.startswith('600') or stock_code.startswith('601'):
                sh_main.append(stock_code)
            elif stock_code.startswith('688'):
                sh_star.append(stock_code)
            elif stock_code.startswith('603') or stock_code.startswith('605'):
                sh_new.append(stock_code)
        
        print(f"\n📋 各板块缓存股票数量:")
        print(f"  深交所主板 (000xxx, 001xxx): {len(main_board)} 只")
        print(f"  深交所中小板 (002xxx): {len(sme_board)} 只")
        print(f"  深交所创业板 (30xxxx): {len(gem_board)} 只")
        print(f"  上交所主板 (600xxx, 601xxx): {len(sh_main)} 只")
        print(f"  上交所科创板 (688xxx): {len(sh_star)} 只")
        print(f"  上交所新股 (603xxx, 605xxx): {len(sh_new)} 只")
        
        print(f"\n🔍 创业板股票详情:")
        for stock_code in sorted(gem_board):
            cache_info = cache_index[stock_code]
            print(f"  {stock_code}: {cache_info['record_count']} 条记录")
        
        return {
            'total': len(cache_index),
            'gem_count': len(gem_board),
            'gem_stocks': gem_board
        }
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def check_test_stocks():
    """检查测试股票的缓存情况"""
    print(f"\n{'='*60}")
    print("测试股票缓存情况检查")
    print("=" * 60)
    
    # 常见的创业板股票代码
    test_stocks = [
        '300472.XSHE',  # 程序尝试查找的股票
        '300689.XSHE',  # 缓存中存在的股票
        '301510.XSHE',  # 缓存中存在的股票
        '300001.XSHE',  # 特锐德（创业板第一股）
        '300002.XSHE',  # 神州泰岳
        '300003.XSHE',  # 乐普医疗
        '300059.XSHE',  # 东方财富
        '300750.XSHE',  # 宁德时代
    ]
    
    try:
        from stock_data_cache import StockDataCache
        
        # 初始化缓存系统（纯缓存模式）
        cache = StockDataCache(cache_dir='stock_cache', cache_only=True)
        
        for stock_code in test_stocks:
            print(f"\n🔍 检查股票: {stock_code}")
            
            # 检查缓存有效性
            is_valid = cache._is_cache_valid(stock_code)
            print(f"  缓存有效性: {is_valid}")
            
            # 检查是否在索引中
            in_index = stock_code in cache.cache_index
            print(f"  在缓存索引中: {in_index}")
            
            # 检查文件是否存在
            normalized_code = cache._normalize_stock_code(stock_code)
            cache_file = cache._get_cache_file_path(normalized_code)
            file_exists = cache_file.exists()
            print(f"  缓存文件存在: {file_exists}")
            print(f"  文件路径: {cache_file}")
            
            # 尝试读取数据
            try:
                data = cache.get_stock_data(stock_code)
                if data is not None:
                    print(f"  ✅ 数据读取成功: {len(data)} 条记录")
                else:
                    print(f"  ❌ 数据读取失败")
            except Exception as e:
                print(f"  ❌ 读取异常: {e}")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

def suggest_solutions():
    """提供解决方案建议"""
    print(f"\n{'='*60}")
    print("解决方案建议")
    print("=" * 60)
    
    print("🔍 问题分析:")
    print("  1. 缓存中创业板股票数量极少（仅2只）")
    print("  2. 程序尝试查找的 300472.XSHE 不在缓存中")
    print("  3. 智能格式匹配机制工作正常，但无法匹配不存在的数据")
    
    print("\n💡 解决方案:")
    print("  方案1: 补充创业板股票缓存数据")
    print("    - 运行 stock_data_precacher.py 补充创业板股票")
    print("    - 确保包含常用的创业板股票代码")
    
    print("  方案2: 优化测试数据")
    print("    - 使用缓存中已存在的股票进行测试")
    print("    - 如 300689.XSHE, 301510.XSHE")
    
    print("  方案3: 增强错误处理")
    print("    - 在纯缓存模式下优雅处理缺失股票")
    print("    - 提供更清晰的错误信息")

def create_test_with_cached_stocks():
    """创建使用已缓存股票的测试文件"""
    print(f"\n{'='*60}")
    print("创建测试文件（使用已缓存股票）")
    print("=" * 60)
    
    try:
        # 使用缓存中存在的股票
        test_data = {
            'secID': ['300689.XSHE', '301510.XSHE'],
            'secShortName': ['澳洋健康', '元道通信'],
            'closePrice': [3.50, 25.80],
            '涨停日期': [20250210, 20250212]
        }
        
        df = pd.DataFrame(test_data)
        test_file = 'test_gem_cached.csv'
        df.to_csv(test_file, index=False, encoding='utf-8-sig')
        
        print(f"✅ 创建测试文件: {test_file}")
        print("📋 文件内容:")
        print(df.to_string(index=False))
        
        return test_file
        
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        return None

if __name__ == "__main__":
    print("🚀 开始诊断创业板股票缓存问题")
    
    # 分析缓存覆盖范围
    coverage_info = analyze_cache_coverage()
    
    # 检查测试股票
    check_test_stocks()
    
    # 提供解决方案
    suggest_solutions()
    
    # 创建测试文件
    test_file = create_test_with_cached_stocks()
    
    print(f"\n{'='*60}")
    print("诊断完成")
    print("=" * 60)
    
    if coverage_info:
        print(f"📊 缓存总股票数: {coverage_info['total']}")
        print(f"📊 创业板股票数: {coverage_info['gem_count']}")
        
        if coverage_info['gem_count'] < 10:
            print("⚠️ 创业板股票缓存数量过少，建议补充缓存数据")
        
        if test_file:
            print(f"✅ 已创建测试文件: {test_file}")
            print("💡 建议使用此文件测试纯缓存模式功能")
