# 股票数据预缓存程序修改说明

## 修改目标
将 `stock_data_precacher.py` 程序的 `--all` 参数设为默认启用，用户无需手动指定即可下载所有A股数据。

## 主要修改内容

### 1. 参数定义修改
```python
# 修改前
parser.add_argument("--all", "-a", action="store_true", help="下载所有A股数据")

# 修改后
parser.add_argument("--all", "-a", action="store_true", default=True, help="下载所有A股数据（默认启用）")
parser.add_argument("--no-all", action="store_true", help="禁用默认的全部A股下载模式，改为交互式选择CSV文件")
```

### 2. 参数处理逻辑
```python
# 新增参数处理逻辑
if args.no_all:
    args.all = False
```

### 3. 帮助信息优化
```python
parser = argparse.ArgumentParser(
    description="股票数据预缓存程序（默认下载所有A股数据）",
    epilog="注意：程序默认启用 --all 模式下载所有A股数据。如需使用CSV文件，请指定 --input 参数；如需禁用全部下载，请使用 --no-all 参数。"
)
```

### 4. 执行逻辑调整
```python
if args.input:
    # 如果指定了输入文件，优先使用输入文件
    stock_codes = load_stock_list_from_csv(args.input)
    logging.info(f"使用指定的输入文件: {args.input}")
elif args.all:
    # 默认模式：下载所有A股数据
    stock_codes = get_all_a_stock_codes()
    logging.info("使用默认模式：下载所有A股数据")
else:
    # 如果禁用了 --all 模式，尝试自动检测当前目录下的CSV文件
    # ... 交互式选择逻辑
```

## 使用方式变化

### 修改前
```bash
# 需要手动指定 --all 参数
python stock_data_precacher.py --all

# 或使用输入文件
python stock_data_precacher.py --input list3.csv
```

### 修改后
```bash
# 直接运行即可下载所有A股数据（默认行为）
python stock_data_precacher.py

# 使用输入文件（覆盖默认行为）
python stock_data_precacher.py --input list3.csv

# 禁用默认的全部下载，进入交互式选择
python stock_data_precacher.py --no-all

# 显式启用全部下载（与默认行为相同）
python stock_data_precacher.py --all
```

## 参数优先级
1. **--input**: 最高优先级，指定输入文件时覆盖所有其他模式
2. **--all**: 默认启用，下载所有A股数据
3. **--no-all**: 禁用默认行为，进入交互式CSV文件选择模式

## 其他功能保持不变
- `--status`: 显示缓存状态
- `--clean`: 清理过期缓存
- `--cache-dir`: 指定缓存目录
- `--workers`: 设置并发线程数
- `--days`: 设置下载天数
- `--force`: 强制刷新缓存

## 兼容性说明
- 原有的 `--all` 参数仍然可用，但现在是默认行为
- 原有的 `--input` 参数功能不变
- 新增的 `--no-all` 参数提供了禁用默认行为的选项
- 所有其他参数和功能保持完全兼容

## 验证方法
可以运行以下命令验证修改效果：
```bash
# 查看帮助信息
python stock_data_precacher.py --help

# 测试默认行为（应该开始下载所有A股数据）
python stock_data_precacher.py --test

# 测试禁用默认行为
python stock_data_precacher.py --no-all
```
