#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试股票数据预缓存程序的串行下载功能
"""

import sys
import os
import tempfile
import time
from unittest.mock import Mock, patch

def test_cache_initialization():
    """测试缓存管理器初始化"""
    print("=== 测试缓存管理器初始化 ===")
    
    try:
        from stock_data_cache import StockDataCache
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 测试不带 max_workers 参数的初始化
            cache = StockDataCache(cache_dir=temp_dir)
            
            # 检查是否成功初始化
            if hasattr(cache, 'cache_dir') and hasattr(cache, 'config'):
                print("  ✓ 缓存管理器初始化成功")
                print(f"    缓存目录: {cache.cache_dir}")
                print(f"    请求延迟: {cache.config['request_delay']} 秒")
                return True
            else:
                print("  ✗ 缓存管理器初始化失败")
                return False
                
    except Exception as e:
        print(f"  ✗ 初始化测试失败: {e}")
        return False

def test_serial_download_logic():
    """测试串行下载逻辑"""
    print("\n=== 测试串行下载逻辑 ===")
    
    try:
        from stock_data_cache import StockDataCache
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            cache = StockDataCache(cache_dir=temp_dir)
            
            # 模拟股票代码列表
            test_stocks = ["000001", "000002", "600001"]
            
            # 模拟 get_stock_data 方法
            original_get_stock_data = cache.get_stock_data
            call_order = []
            
            def mock_get_stock_data(stock_code, start_date=None, end_date=None, force_refresh=False):
                call_order.append(stock_code)
                time.sleep(0.1)  # 模拟处理时间
                # 模拟返回数据
                import pandas as pd
                from datetime import datetime
                return pd.DataFrame({
                    'date': [datetime.now()],
                    'open': [10.0],
                    'close': [10.5],
                    'high': [11.0],
                    'low': [9.5],
                    'volume': [1000000],
                    'stock_code': [stock_code]
                })
            
            cache.get_stock_data = mock_get_stock_data
            
            # 执行批量下载
            start_time = time.time()
            results = cache.batch_download(test_stocks)
            end_time = time.time()
            
            # 验证结果
            print(f"  处理顺序: {call_order}")
            print(f"  处理时间: {end_time - start_time:.2f} 秒")
            print(f"  下载结果: {results}")
            
            # 检查是否按顺序处理
            if call_order == test_stocks:
                print("  ✓ 股票按顺序串行处理")
                return True
            else:
                print("  ✗ 股票处理顺序不正确")
                return False
                
    except Exception as e:
        print(f"  ✗ 串行下载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_precacher_arguments():
    """测试预缓存程序参数"""
    print("\n=== 测试预缓存程序参数 ===")
    
    try:
        import argparse
        
        # 模拟参数解析器（基于修改后的代码）
        parser = argparse.ArgumentParser(
            description="股票数据预缓存程序（默认下载所有A股数据）",
            epilog="注意：程序默认启用 --all 模式下载所有A股数据。如需使用CSV文件，请指定 --input 参数；如需禁用全部下载，请使用 --no-all 参数。"
        )
        parser.add_argument("--input", "-i", type=str, help="输入CSV文件路径（包含股票代码），指定后将覆盖默认的全部A股模式")
        parser.add_argument("--all", "-a", action="store_true", default=True, help="下载所有A股数据（默认启用）")
        parser.add_argument("--no-all", action="store_true", help="禁用默认的全部A股下载模式，改为交互式选择CSV文件")
        parser.add_argument("--cache-dir", "-c", type=str, default="stock_cache", help="缓存目录路径")
        parser.add_argument("--days", "-d", type=int, default=365, help="下载天数（默认365天）")
        parser.add_argument("--force", "-f", action="store_true", help="强制刷新所有缓存")
        parser.add_argument("--status", "-s", action="store_true", help="显示缓存状态")
        parser.add_argument("--clean", action="store_true", help="清理过期缓存")
        
        # 测试参数解析
        test_args = []
        args = parser.parse_args(test_args)
        
        # 检查是否移除了 workers 参数
        if not hasattr(args, 'workers'):
            print("  ✓ 成功移除 --workers 参数")
        else:
            print("  ✗ --workers 参数仍然存在")
            return False
        
        # 检查其他参数是否正常
        expected_attrs = ['input', 'all', 'no_all', 'cache_dir', 'days', 'force', 'status', 'clean']
        missing_attrs = [attr for attr in expected_attrs if not hasattr(args, attr)]
        
        if not missing_attrs:
            print("  ✓ 所有必要参数都存在")
            return True
        else:
            print(f"  ✗ 缺少参数: {missing_attrs}")
            return False
            
    except Exception as e:
        print(f"  ✗ 参数测试失败: {e}")
        return False

def test_request_delay():
    """测试请求延迟配置"""
    print("\n=== 测试请求延迟配置 ===")
    
    try:
        from stock_data_cache import StockDataCache
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            cache = StockDataCache(cache_dir=temp_dir)
            
            # 检查请求延迟配置
            request_delay = cache.config.get('request_delay', 0)
            
            if request_delay >= 1.0:
                print(f"  ✓ 请求延迟设置合理: {request_delay} 秒")
                return True
            else:
                print(f"  ✗ 请求延迟过短: {request_delay} 秒")
                return False
                
    except Exception as e:
        print(f"  ✗ 请求延迟测试失败: {e}")
        return False

def main():
    """主函数"""
    print("股票数据预缓存程序串行下载功能测试")
    print("=" * 60)
    
    results = []
    
    try:
        results.append(("缓存管理器初始化", test_cache_initialization()))
        results.append(("串行下载逻辑", test_serial_download_logic()))
        results.append(("预缓存程序参数", test_precacher_arguments()))
        results.append(("请求延迟配置", test_request_delay()))
        
        # 汇总结果
        print("\n" + "=" * 60)
        print("测试结果汇总:")
        
        all_passed = True
        for test_name, passed in results:
            status = "✓ 通过" if passed else "✗ 失败"
            print(f"  {status} {test_name}")
            if not passed:
                all_passed = False
        
        print("\n" + "=" * 60)
        if all_passed:
            print("🎉 所有测试都通过了！")
            print("\n修改总结:")
            print("1. ✅ 移除了 ThreadPoolExecutor 并发处理")
            print("2. ✅ 改为串行 for 循环处理")
            print("3. ✅ 移除了 --workers 参数")
            print("4. ✅ 增加了请求延迟（1.5秒）")
            print("5. ✅ 保持了所有现有功能")
            
            print("\n串行处理特点:")
            print("- 一次只处理一只股票")
            print("- 避免API并发限制")
            print("- 更稳定的下载过程")
            print("- 适当的请求间隔")
            
            print("\n下一步:")
            print("- 可以运行 python stock_data_precacher.py 测试串行下载")
            print("- 观察下载过程中的日志输出")
            print("- 验证API调用的稳定性")
        else:
            print("❌ 部分测试失败，请检查修改内容")
        
        return all_passed
        
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
