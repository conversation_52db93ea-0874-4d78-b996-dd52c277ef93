#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创业板股票缓存补充工具
"""

import pandas as pd
import os
from pathlib import Path

def create_gem_stock_list():
    """创建创业板股票列表"""
    print("=" * 60)
    print("创建创业板股票补充列表")
    print("=" * 60)
    
    # 常见的创业板股票（按市值和活跃度选择）
    gem_stocks = [
        # 创业板龙头股票
        ('300001.XSHE', '特锐德'),
        ('300002.XSHE', '神州泰岳'),
        ('300003.XSHE', '乐普医疗'),
        ('300059.XSHE', '东方财富'),
        ('300750.XSHE', '宁德时代'),
        
        # 问题股票
        ('300472.XSHE', '新元科技'),
        
        # 其他活跃创业板股票
        ('300015.XSHE', '爱尔眼科'),
        ('300033.XSHE', '同花顺'),
        ('300142.XSHE', '沃森生物'),
        ('300144.XSHE', '宋城演艺'),
        ('300347.XSHE', '泰格医药'),
        ('300408.XSHE', '三环集团'),
        ('300413.XSHE', '芒果超媒'),
        ('300498.XSHE', '温氏股份'),
        ('300760.XSHE', '迈瑞医疗'),
        
        # 新兴创业板股票
        ('301029.XSHE', '怡合达'),
        ('301236.XSHE', '软通动力'),
        ('301269.XSHE', '华大九天'),
        ('301308.XSHE', '江波龙'),
        ('301319.XSHE', '唯特偶'),
    ]
    
    # 创建DataFrame
    df = pd.DataFrame(gem_stocks, columns=['secID', 'secShortName'])
    
    # 添加模拟数据
    df['closePrice'] = [10.0 + i * 2.5 for i in range(len(df))]
    df['涨停日期'] = [20250210, 20250211, 20250212] * (len(df) // 3 + 1)
    df = df.head(len(gem_stocks))
    
    # 保存文件
    output_file = 'gem_stocks_to_cache.csv'
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f"✅ 创建创业板股票列表: {output_file}")
    print(f"📊 包含股票数量: {len(df)}")
    print("\n📋 股票列表预览:")
    print(df.head(10).to_string(index=False))
    
    return output_file

def check_current_gem_cache():
    """检查当前创业板缓存情况"""
    print(f"\n{'='*60}")
    print("检查当前创业板缓存情况")
    print("=" * 60)
    
    try:
        import json
        
        cache_index_file = Path('stock_cache/metadata/cache_index.json')
        if cache_index_file.exists():
            with open(cache_index_file, 'r', encoding='utf-8') as f:
                cache_index = json.load(f)
            
            # 统计创业板股票
            gem_stocks = [code for code in cache_index.keys() if code.startswith('30')]
            
            print(f"📊 当前缓存统计:")
            print(f"  总股票数: {len(cache_index)}")
            print(f"  创业板股票数: {len(gem_stocks)}")
            
            if gem_stocks:
                print(f"\n📋 已缓存的创业板股票:")
                for stock in sorted(gem_stocks):
                    cache_info = cache_index[stock]
                    print(f"  {stock}: {cache_info['record_count']} 条记录")
            else:
                print("❌ 当前无创业板股票缓存")
            
            return len(gem_stocks)
        else:
            print("❌ 缓存索引文件不存在")
            return 0
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return 0

def provide_caching_instructions():
    """提供缓存补充指导"""
    print(f"\n{'='*60}")
    print("缓存补充指导")
    print("=" * 60)
    
    print("🔧 补充创业板股票缓存的步骤:")
    print()
    print("1. 使用生成的股票列表文件:")
    print("   python stock_data_precacher.py --input gem_stocks_to_cache.csv")
    print()
    print("2. 或者手动添加特定股票:")
    print("   python stock_data_precacher.py --stocks 300472.XSHE,300001.XSHE,300059.XSHE")
    print()
    print("3. 验证缓存补充结果:")
    print("   python simple_gem_check.py")
    print()
    print("4. 使用补充后的缓存测试:")
    print("   python standalone_stock_scorer.py")
    
    print(f"\n💡 临时解决方案:")
    print("   使用已缓存股票测试: test_cached_stocks.csv")
    print("   该文件包含已确认缓存的股票，可立即测试纯缓存模式")

def create_test_script():
    """创建测试脚本"""
    test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试创业板股票缓存补充效果
"""

def test_gem_cache_supplement():
    """测试创业板缓存补充效果"""
    try:
        from stock_data_cache import StockDataCache
        
        cache = StockDataCache(cache_dir='stock_cache', cache_only=True)
        
        # 测试目标股票
        target_stocks = [
            '300472.XSHE',  # 原问题股票
            '300001.XSHE',  # 特锐德
            '300059.XSHE',  # 东方财富
            '300750.XSHE',  # 宁德时代
        ]
        
        print("测试创业板股票缓存:")
        success_count = 0
        
        for stock_code in target_stocks:
            data = cache.get_stock_data(stock_code)
            if data is not None:
                print(f"  ✅ {stock_code}: {len(data)} 条记录")
                success_count += 1
            else:
                print(f"  ❌ {stock_code}: 未缓存")
        
        print(f"\\n📊 成功率: {success_count}/{len(target_stocks)} ({success_count/len(target_stocks)*100:.1f}%)")
        
        if success_count == len(target_stocks):
            print("🎉 创业板股票缓存补充成功！")
        else:
            print("⚠️ 部分股票仍需补充缓存")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_gem_cache_supplement()
'''
    
    with open('test_gem_supplement.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print(f"\n✅ 创建测试脚本: test_gem_supplement.py")

if __name__ == "__main__":
    print("🚀 创业板股票缓存补充工具")
    
    # 检查当前缓存情况
    current_gem_count = check_current_gem_cache()
    
    # 创建补充列表
    gem_file = create_gem_stock_list()
    
    # 创建测试脚本
    create_test_script()
    
    # 提供指导
    provide_caching_instructions()
    
    print(f"\n{'='*60}")
    print("总结")
    print("=" * 60)
    print(f"📊 当前创业板缓存: {current_gem_count} 只")
    print(f"📋 补充列表文件: {gem_file}")
    print(f"🧪 测试脚本: test_gem_supplement.py")
    print(f"🔧 临时测试文件: test_cached_stocks.csv")
    
    if current_gem_count < 5:
        print("\n⚠️ 建议立即补充创业板股票缓存")
        print("💡 或使用 test_cached_stocks.csv 进行临时测试")
