#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票缓存文件迁移工具
将旧格式的缓存文件（股票代码.csv）迁移到新格式（股票代码_交易所代码.csv）
"""

import os
import sys
import pandas as pd
import logging
from pathlib import Path
import shutil

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('cache_migration.log', encoding='utf-8')
        ]
    )

def get_exchange_code(stock_code: str) -> str:
    """获取股票交易所代码"""
    try:
        # 标准化股票代码
        if '.' in stock_code:
            normalized_code = stock_code.split('.')[0]
        else:
            normalized_code = stock_code
        
        # 根据股票代码判断交易所
        if normalized_code.startswith('60') or normalized_code.startswith('68'):
            # 60开头：上海主板，68开头：科创板
            return "XSHG"
        elif (normalized_code.startswith('00') or 
              normalized_code.startswith('002') or 
              normalized_code.startswith('003') or 
              normalized_code.startswith('30')):
            # 00开头：深圳主板，002/003/30开头：创业板
            return "XSHE"
        else:
            # 默认返回深圳交易所
            return "XSHE"
            
    except Exception as e:
        logging.warning(f"获取股票 {stock_code} 交易所代码失败: {e}")
        return "XSHE"

def migrate_cache_files(cache_dir: str = "stock_cache", dry_run: bool = False):
    """
    迁移缓存文件到新格式
    
    Args:
        cache_dir: 缓存目录路径
        dry_run: 是否为试运行模式（不实际执行迁移）
    """
    cache_path = Path(cache_dir)
    data_dir = cache_path / "data"
    
    if not data_dir.exists():
        logging.error(f"缓存数据目录不存在: {data_dir}")
        return
    
    # 统计信息
    total_files = 0
    migrated_files = 0
    skipped_files = 0
    error_files = 0
    
    logging.info(f"开始扫描缓存目录: {data_dir}")
    logging.info(f"试运行模式: {'是' if dry_run else '否'}")
    
    # 遍历所有子目录
    for sub_dir in data_dir.iterdir():
        if not sub_dir.is_dir():
            continue
            
        logging.info(f"处理子目录: {sub_dir.name}")
        
        # 遍历子目录中的所有文件
        for file_path in sub_dir.iterdir():
            if not file_path.is_file():
                continue
                
            total_files += 1
            file_name = file_path.name
            
            # 检查是否为缓存文件
            if file_name.endswith('.csv') or file_name.endswith('.parquet'):
                # 提取股票代码
                stock_code = file_path.stem
                
                # 检查是否已经是新格式（包含下划线分隔的交易所代码）
                if '_' in stock_code and (stock_code.endswith('_XSHG') or stock_code.endswith('_XSHE')):
                    logging.debug(f"跳过新格式文件: {file_name}")
                    skipped_files += 1
                    continue
                
                # 获取交易所代码
                exchange_code = get_exchange_code(stock_code)
                
                # 生成新文件名
                new_file_name = f"{stock_code}_{exchange_code}{file_path.suffix}"
                new_file_path = file_path.parent / new_file_name
                
                # 检查新文件是否已存在
                if new_file_path.exists():
                    logging.warning(f"新格式文件已存在，跳过: {new_file_name}")
                    skipped_files += 1
                    continue
                
                try:
                    if not dry_run:
                        # 执行文件重命名
                        shutil.move(str(file_path), str(new_file_path))
                        logging.info(f"迁移成功: {file_name} -> {new_file_name}")
                    else:
                        logging.info(f"[试运行] 将迁移: {file_name} -> {new_file_name}")
                    
                    migrated_files += 1
                    
                except Exception as e:
                    logging.error(f"迁移失败 {file_name}: {e}")
                    error_files += 1
            else:
                logging.debug(f"跳过非缓存文件: {file_name}")
                skipped_files += 1
    
    # 输出统计结果
    print("\n" + "=" * 60)
    print("缓存文件迁移完成")
    print("=" * 60)
    print(f"总文件数: {total_files}")
    print(f"迁移文件数: {migrated_files}")
    print(f"跳过文件数: {skipped_files}")
    print(f"错误文件数: {error_files}")
    print(f"成功率: {migrated_files/(migrated_files+error_files)*100:.1f}%" if (migrated_files+error_files) > 0 else "100.0%")
    
    if dry_run:
        print("\n注意: 这是试运行模式，实际文件未被修改")
        print("要执行实际迁移，请运行: python migrate_cache_to_exchange_format.py --execute")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="股票缓存文件迁移工具")
    parser.add_argument("--cache-dir", "-c", type=str, default="stock_cache", help="缓存目录路径")
    parser.add_argument("--execute", action="store_true", help="执行实际迁移（默认为试运行模式）")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 确认执行
    if args.execute:
        confirm = input("确认要执行缓存文件迁移吗？这将重命名现有的缓存文件。(y/N): ").strip().lower()
        if confirm != 'y':
            print("操作已取消")
            return
    
    # 执行迁移
    migrate_cache_files(cache_dir=args.cache_dir, dry_run=not args.execute)

if __name__ == "__main__":
    main()
