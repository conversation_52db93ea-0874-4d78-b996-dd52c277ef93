#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终修复效果
"""

import pandas as pd
import os

def test_cache_only_mode():
    """测试纯缓存模式"""
    print("=" * 60)
    print("测试纯缓存模式修复效果")
    print("=" * 60)
    
    try:
        from stock_data_cache import StockDataCache
        
        # 初始化缓存系统（纯缓存模式）
        cache = StockDataCache(cache_dir='stock_cache', cache_only=True)
        print("✅ 缓存系统初始化成功（纯缓存模式）")
        
        # 测试问题股票
        test_codes = ['000002.XSHE', '000004.XSHE']
        
        for stock_code in test_codes:
            print(f"\n🔍 测试股票: {stock_code}")
            
            # 测试缓存有效性
            is_valid = cache._is_cache_valid(stock_code)
            print(f"  缓存有效性: {is_valid}")
            
            # 测试数据读取
            data = cache.get_stock_data(
                stock_code, 
                start_date='2024-09-15', 
                end_date='2025-02-12'
            )
            
            if data is not None:
                print(f"  ✅ 成功读取: {len(data)} 条记录")
                print(f"  📊 日期范围: {data['date'].min()} 到 {data['date'].max()}")
            else:
                print(f"  ❌ 读取失败（纯缓存模式下正常，说明API调用被阻止）")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_scorer_cache_only():
    """测试评分器纯缓存模式"""
    print(f"\n{'='*60}")
    print("测试评分器纯缓存模式")
    print("=" * 60)
    
    try:
        from standalone_stock_scorer import StockScorer
        
        # 创建评分器（纯缓存模式）
        scorer = StockScorer(cache_only=True, cache_dir='stock_cache')
        print("✅ 评分器初始化成功（纯缓存模式）")
        
        # 创建测试数据
        test_data = pd.DataFrame([{
            'secID': '000002.XSHE',
            'secShortName': '万科A',
            'closePrice': 8.50,
            '涨停日期': 20250212
        }])
        
        print("\n🔍 测试单只股票评分...")
        
        # 测试评分（应该不会调用API）
        result = scorer.score_single_stock(
            test_data, 
            '000002.XSHE', 
            '万科A', 
            limit_up_date=20250212
        )
        
        if result:
            if result.get('skipped', False):
                print(f"✅ 股票被正确跳过: {result.get('skip_reason', '未知原因')}")
                print("✅ 纯缓存模式工作正常，没有调用API")
                return True
            else:
                print(f"✅ 评分成功（使用缓存数据）:")
                print(f"  总评分: {result.get('total_score', 'N/A')}")
                return True
        else:
            print("❌ 评分失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始测试最终修复效果")
    
    # 测试1：缓存系统纯缓存模式
    test1_result = test_cache_only_mode()
    
    # 测试2：评分器纯缓存模式
    test2_result = test_scorer_cache_only()
    
    print(f"\n{'='*60}")
    print("测试结果汇总")
    print("=" * 60)
    print(f"缓存系统纯缓存模式: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"评分器纯缓存模式: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if all([test1_result, test2_result]):
        print("\n🎉 修复成功！程序现在完全使用纯缓存模式")
        print("✅ 不会再调用任何外部API")
    else:
        print("\n⚠️ 修复未完全成功，需要进一步调试")
