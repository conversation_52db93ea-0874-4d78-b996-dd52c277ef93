#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试缓存修复效果
"""

import pandas as pd
import os
from datetime import datetime

def test_cache_fix():
    """测试缓存修复效果"""
    print("=" * 60)
    print("测试缓存修复效果")
    print("=" * 60)
    
    try:
        from stock_data_cache import StockDataCache
        
        # 初始化缓存系统
        cache = StockDataCache(cache_dir='stock_cache')
        print("✅ 缓存系统初始化成功")
        
        # 测试股票代码（问题股票）
        test_codes = ['000002.XSHE', '000004.XSHE', '000002', '000004']
        
        for stock_code in test_codes:
            print(f"\n🔍 测试股票代码: {stock_code}")
            
            # 测试缓存有效性检查
            is_valid = cache._is_cache_valid(stock_code)
            print(f"  缓存有效性: {is_valid}")
            
            # 测试数据读取（基于涨停日期的日期范围）
            start_date = '2024-09-15'
            end_date = '2025-02-12'
            
            data = cache.get_stock_data(stock_code, start_date, end_date)
            
            if data is not None:
                print(f"  ✅ 成功读取数据: {len(data)} 条记录")
                print(f"  📊 日期范围: {data['date'].min()} 到 {data['date'].max()}")
                
                # 检查数据是否足够（至少60条）
                if len(data) >= 60:
                    print(f"  ✅ 数据充足: {len(data)} >= 60")
                else:
                    print(f"  ⚠️ 数据不足: {len(data)} < 60")
            else:
                print(f"  ❌ 读取数据失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_stock_scorer_integration():
    """测试股票评分器集成"""
    print(f"\n{'='*60}")
    print("测试股票评分器集成")
    print("=" * 60)
    
    try:
        from standalone_stock_scorer import StockScorer
        
        # 创建评分器（纯缓存模式）
        scorer = StockScorer(cache_only=True, cache_dir='stock_cache')
        print("✅ 评分器初始化成功（纯缓存模式）")
        
        # 创建测试数据
        test_data = pd.DataFrame([{
            'secID': '000002.XSHE',
            'secShortName': '万科A',
            'closePrice': 8.50,
            '涨停日期': 20250212
        }])
        
        print("\n🔍 测试单只股票评分...")
        
        # 测试评分
        result = scorer.score_single_stock(
            test_data, 
            '000002.XSHE', 
            '万科A', 
            limit_up_date=20250212
        )
        
        if result:
            if result.get('skipped', False):
                print(f"⚠️ 股票被跳过: {result.get('skip_reason', '未知原因')}")
                return False
            else:
                print(f"✅ 评分成功:")
                print(f"  股票代码: {result['stock_code']}")
                print(f"  股票名称: {result['stock_name']}")
                print(f"  总评分: {result.get('total_score', 'N/A')}")
                print(f"  评级: {result.get('grade', 'N/A')}")
                print(f"  数据点数: {result.get('data_points', 'N/A')}")
                print(f"  数据源: {result.get('data_source', 'N/A')}")
                return True
        else:
            print("❌ 评分失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_csv():
    """创建测试CSV文件"""
    test_data = {
        'secID': ['000002.XSHE', '000004.XSHE'],
        'secShortName': ['万科A', '国华网安'],
        'closePrice': [8.50, 15.20],
        '涨停日期': [20250212, 20250205]
    }
    
    df = pd.DataFrame(test_data)
    test_file = 'test_cache_fix.csv'
    df.to_csv(test_file, index=False, encoding='utf-8-sig')
    print(f"✅ 创建测试文件: {test_file}")
    return test_file

def test_batch_processing():
    """测试批量处理"""
    print(f"\n{'='*60}")
    print("测试批量处理")
    print("=" * 60)
    
    try:
        from standalone_stock_scorer import StockScorer
        
        # 创建测试文件
        test_file = create_test_csv()
        
        # 创建评分器
        scorer = StockScorer(cache_only=True, cache_dir='stock_cache')
        
        print(f"📊 开始批量评分测试...")
        
        # 执行批量评分
        output_file = scorer.batch_score_stocks(test_file)
        
        if output_file and os.path.exists(output_file):
            print(f"✅ 批量评分成功，输出文件: {output_file}")
            
            # 读取结果文件
            result_df = pd.read_csv(output_file, encoding='utf-8-sig')
            print(f"📋 结果摘要:")
            print(f"  总股票数: {len(result_df)}")
            
            successful = result_df[result_df['状态'] == '成功评分']
            skipped = result_df[result_df['状态'] == '已跳过']
            
            print(f"  成功评分: {len(successful)} 只")
            print(f"  跳过股票: {len(skipped)} 只")
            
            if len(successful) > 0:
                print(f"  平均评分: {successful['总评分'].mean():.1f}")
            
            return len(successful) > 0
        else:
            print("❌ 批量评分失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理测试文件
        for file in ['test_cache_fix.csv']:
            if os.path.exists(file):
                os.remove(file)

if __name__ == "__main__":
    print("🚀 开始测试缓存修复效果")
    
    # 测试1：基础缓存功能
    test1_result = test_cache_fix()
    
    # 测试2：评分器集成
    test2_result = test_stock_scorer_integration()
    
    # 测试3：批量处理
    test3_result = test_batch_processing()
    
    print(f"\n{'='*60}")
    print("测试结果汇总")
    print("=" * 60)
    print(f"基础缓存功能: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"评分器集成: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"批量处理: {'✅ 通过' if test3_result else '❌ 失败'}")
    
    if all([test1_result, test2_result, test3_result]):
        print("\n🎉 所有测试通过！缓存问题已修复")
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试")
