#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强的股票数据预缓存程序功能
"""

import os
import sys
import tempfile
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
import time

def test_exponential_backoff():
    """测试指数退避重试机制"""
    print("=== 测试指数退避重试机制 ===")
    
    def simulate_exponential_backoff(max_retries=10, base_delay=10):
        """模拟指数退避算法"""
        delays = []
        for attempt in range(max_retries):
            if attempt > 0:  # 第一次不需要等待
                wait_time = base_delay * (2 ** (attempt - 1))
                delays.append(wait_time)
        return delays
    
    delays = simulate_exponential_backoff()
    
    print("  指数退避等待时间序列:")
    total_wait = 0
    for i, delay in enumerate(delays):
        total_wait += delay
        print(f"    第 {i+1} 次重试: 等待 {delay} 秒")
    
    print(f"  总重试次数: {len(delays)} 次")
    print(f"  总等待时间: {total_wait} 秒 ({total_wait/60:.1f} 分钟)")
    print(f"  最大等待时间: {max(delays)} 秒")
    
    # 验证指数增长
    expected_delays = [10 * (2 ** i) for i in range(9)]
    if delays == expected_delays:
        print("  ✓ 指数退避算法正确")
        return True
    else:
        print("  ✗ 指数退避算法错误")
        print(f"    期望: {expected_delays}")
        print(f"    实际: {delays}")
        return False

def test_cache_validation():
    """测试缓存文件验证功能"""
    print("\n=== 测试缓存文件验证功能 ===")
    
    try:
        from stock_data_cache import StockDataCache
        
        # 创建临时缓存管理器
        with tempfile.TemporaryDirectory() as temp_dir:
            cache = StockDataCache(cache_dir=temp_dir)
            
            # 创建测试数据
            test_stock = "000001"
            dates = pd.date_range(start=datetime.now() - timedelta(days=30), end=datetime.now(), freq='D')
            
            # 测试用例1: 有效的缓存文件
            valid_data = pd.DataFrame({
                'date': dates,
                'open': [10.0] * len(dates),
                'close': [10.5] * len(dates),
                'high': [11.0] * len(dates),
                'low': [9.5] * len(dates),
                'volume': [1000000] * len(dates),
                'stock_code': [test_stock] * len(dates)
            })
            
            cache_file = cache._get_cache_file_path(test_stock, use_exchange_suffix=True)
            cache_file.parent.mkdir(parents=True, exist_ok=True)
            valid_data.to_csv(cache_file, index=False, encoding='utf-8')
            
            if cache._is_cache_file_valid(test_stock):
                print("  ✓ 有效缓存文件检测正确")
            else:
                print("  ✗ 有效缓存文件检测失败")
                return False
            
            # 测试用例2: 空文件
            empty_file = cache._get_cache_file_path("000002", use_exchange_suffix=True)
            empty_file.parent.mkdir(parents=True, exist_ok=True)
            empty_file.write_text("")
            
            if not cache._is_cache_file_valid("000002"):
                print("  ✓ 空文件检测正确")
            else:
                print("  ✗ 空文件检测失败")
                return False
            
            # 测试用例3: 缺少必要列的文件
            invalid_data = pd.DataFrame({
                'date': dates,
                'price': [10.0] * len(dates),  # 缺少 open, close, high, low
            })
            
            invalid_file = cache._get_cache_file_path("000003", use_exchange_suffix=True)
            invalid_file.parent.mkdir(parents=True, exist_ok=True)
            invalid_data.to_csv(invalid_file, index=False, encoding='utf-8')
            
            if not cache._is_cache_file_valid("000003"):
                print("  ✓ 无效数据格式检测正确")
            else:
                print("  ✗ 无效数据格式检测失败")
                return False
            
            # 测试用例4: 不存在的文件
            if not cache._is_cache_file_valid("999999"):
                print("  ✓ 不存在文件检测正确")
            else:
                print("  ✗ 不存在文件检测失败")
                return False
            
            return True
            
    except ImportError:
        print("  ✗ 无法导入 stock_data_cache 模块")
        return False
    except Exception as e:
        print(f"  ✗ 测试过程中发生错误: {e}")
        return False

def test_smart_skip_logic():
    """测试智能跳过逻辑"""
    print("\n=== 测试智能跳过逻辑 ===")
    
    def simulate_smart_skip(stock_codes, cached_stocks):
        """模拟智能跳过逻辑"""
        original_count = len(stock_codes)
        skipped_stocks = []
        remaining_stocks = []
        
        for code in stock_codes:
            if code in cached_stocks:
                skipped_stocks.append(code)
            else:
                remaining_stocks.append(code)
        
        return {
            'original_count': original_count,
            'skipped_count': len(skipped_stocks),
            'remaining_count': len(remaining_stocks),
            'skipped_stocks': skipped_stocks,
            'remaining_stocks': remaining_stocks
        }
    
    # 测试用例
    test_cases = [
        {
            'name': '全部需要下载',
            'stock_codes': ['000001', '000002', '000003'],
            'cached_stocks': [],
            'expected_remaining': 3
        },
        {
            'name': '部分已缓存',
            'stock_codes': ['000001', '000002', '000003', '000004'],
            'cached_stocks': ['000001', '000003'],
            'expected_remaining': 2
        },
        {
            'name': '全部已缓存',
            'stock_codes': ['000001', '000002'],
            'cached_stocks': ['000001', '000002'],
            'expected_remaining': 0
        }
    ]
    
    all_passed = True
    for case in test_cases:
        result = simulate_smart_skip(case['stock_codes'], case['cached_stocks'])
        
        if result['remaining_count'] == case['expected_remaining']:
            print(f"  ✓ {case['name']}: 跳过 {result['skipped_count']}, 剩余 {result['remaining_count']}")
        else:
            print(f"  ✗ {case['name']}: 期望剩余 {case['expected_remaining']}, 实际剩余 {result['remaining_count']}")
            all_passed = False
    
    return all_passed

def test_progress_reporting():
    """测试进度报告功能"""
    print("\n=== 测试进度报告功能 ===")
    
    def simulate_progress_reporting(total_stocks, batch_size=50):
        """模拟进度报告"""
        progress_points = []
        
        for completed in range(1, total_stocks + 1):
            if completed % batch_size == 0 or completed == total_stocks:
                progress = (completed / total_stocks) * 100
                progress_points.append({
                    'completed': completed,
                    'total': total_stocks,
                    'percentage': progress
                })
        
        return progress_points
    
    # 测试不同规模的股票数量
    test_cases = [100, 500, 1000, 5000]
    
    for total in test_cases:
        progress_points = simulate_progress_reporting(total)
        print(f"  股票总数 {total}: 将报告 {len(progress_points)} 次进度")
        
        # 验证最后一次进度是100%
        if progress_points and progress_points[-1]['percentage'] == 100.0:
            print(f"    ✓ 最终进度正确: {progress_points[-1]['percentage']:.1f}%")
        else:
            print(f"    ✗ 最终进度错误")
            return False
    
    return True

def main():
    """主函数"""
    print("增强的股票数据预缓存程序功能测试")
    print("=" * 60)
    
    results = []
    
    try:
        results.append(("指数退避重试机制", test_exponential_backoff()))
        results.append(("缓存文件验证", test_cache_validation()))
        results.append(("智能跳过逻辑", test_smart_skip_logic()))
        results.append(("进度报告功能", test_progress_reporting()))
        
        # 汇总结果
        print("\n" + "=" * 60)
        print("测试结果汇总:")
        
        all_passed = True
        for test_name, passed in results:
            status = "✓ 通过" if passed else "✗ 失败"
            print(f"  {status} {test_name}")
            if not passed:
                all_passed = False
        
        print("\n" + "=" * 60)
        if all_passed:
            print("🎉 所有测试都通过了！")
            print("\n新增功能总结:")
            print("1. ✅ 指数退避重试机制（最多10次，等待时间指数增长）")
            print("2. ✅ 智能跳过已下载股票（检查缓存文件有效性）")
            print("3. ✅ 增强的进度报告（详细统计和进度显示）")
            print("4. ✅ 改进的日志记录（更清晰的状态信息）")
            
            print("\n重试机制特点:")
            print("- 最大重试次数: 10次")
            print("- 等待时间: 10秒 → 20秒 → 40秒 → 80秒 → ...")
            print("- 总等待时间: 约85分钟（最坏情况）")
            
            print("\n智能跳过特点:")
            print("- 检查缓存文件存在性")
            print("- 验证文件内容完整性")
            print("- 支持断点续传")
            print("- 减少不必要的API调用")
        else:
            print("❌ 部分测试失败，请检查实现")
        
        return all_passed
        
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
