#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试缓存数据读取问题
"""

import os
import sys
from pathlib import Path
import pandas as pd
from datetime import datetime, timedelta

def debug_cache_lookup():
    """调试缓存查找问题"""
    print("=" * 60)
    print("调试缓存数据读取问题")
    print("=" * 60)
    
    try:
        from stock_data_cache import StockDataCache
        
        # 初始化缓存系统
        cache = StockDataCache(cache_dir='stock_cache')
        print("✅ 缓存系统初始化成功")
        
        # 测试股票代码
        test_codes = ['000002.XSHE', '000004.XSHE', '000002', '000004']
        
        for stock_code in test_codes:
            print(f"\n🔍 测试股票代码: {stock_code}")
            
            # 1. 检查代码标准化
            normalized_code = cache._normalize_stock_code(stock_code)
            print(f"  标准化后: {normalized_code}")
            
            # 2. 检查缓存文件路径
            cache_file = cache._get_cache_file_path(normalized_code)
            print(f"  缓存文件路径: {cache_file}")
            print(f"  文件是否存在: {cache_file.exists()}")
            
            # 3. 检查缓存索引
            is_in_index = normalized_code in cache.cache_index
            print(f"  在缓存索引中: {is_in_index}")
            
            if is_in_index:
                cache_info = cache.cache_index[normalized_code]
                print(f"  缓存信息: {cache_info}")
                
                # 4. 检查缓存有效性
                is_valid = cache._is_cache_valid(normalized_code)
                print(f"  缓存有效: {is_valid}")
            
            # 5. 尝试读取数据
            try:
                # 测试不同的日期范围
                test_ranges = [
                    ('2024-09-15', '2025-02-12'),  # 基于涨停日期的范围
                    ('2024-01-01', '2025-06-30'),  # 更大的范围
                    (None, None)  # 默认范围
                ]
                
                for start_date, end_date in test_ranges:
                    print(f"\n  📅 测试日期范围: {start_date} 到 {end_date}")
                    data = cache.get_stock_data(stock_code, start_date, end_date)
                    
                    if data is not None:
                        print(f"    ✅ 成功读取数据: {len(data)} 条记录")
                        print(f"    📊 日期范围: {data['date'].min()} 到 {data['date'].max()}")
                        break
                    else:
                        print(f"    ❌ 读取失败")
                        
            except Exception as e:
                print(f"  ❌ 读取异常: {e}")
                import traceback
                traceback.print_exc()
        
        # 6. 检查缓存索引中的所有键
        print(f"\n📋 缓存索引中的前10个键:")
        for i, key in enumerate(list(cache.cache_index.keys())[:10]):
            print(f"  {i+1}. {key}")
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

def debug_stock_scorer():
    """调试股票评分器的缓存使用"""
    print(f"\n{'='*60}")
    print("调试股票评分器缓存使用")
    print("=" * 60)
    
    try:
        from standalone_stock_scorer import RealDataService
        
        # 初始化数据服务（纯缓存模式）
        data_service = RealDataService(cache_only=True, cache_dir='stock_cache')
        print("✅ 数据服务初始化成功（纯缓存模式）")
        
        # 测试股票代码
        test_codes = ['000002.XSHE', '000004.XSHE']
        
        for stock_code in test_codes:
            print(f"\n🔍 测试股票: {stock_code}")
            
            # 模拟基于涨停日期的查询
            limit_up_date = '20250212'  # 2025年2月12日
            
            print(f"  涨停日期: {limit_up_date}")
            
            # 调用数据获取方法
            data = data_service.get_stock_price_history(
                stock_code=stock_code,
                market_type='A',
                limit_up_date=limit_up_date
            )
            
            if data is not None:
                print(f"  ✅ 成功获取数据: {len(data)} 条记录")
                print(f"  📊 日期范围: {data['date'].min()} 到 {data['date'].max()}")
            else:
                print(f"  ❌ 获取数据失败")
                
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_cache_lookup()
    debug_stock_scorer()
