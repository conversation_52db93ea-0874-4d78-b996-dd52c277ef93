#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据缓存管理系统
提供高效的本地数据缓存和管理功能
"""

import os
import pandas as pd
import numpy as np
import logging
import json
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
import akshare as ak
from typing import Optional, Dict, List, Tuple
import time
import hashlib
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

class StockDataCache:
    """股票数据缓存管理类"""
    
    def __init__(self, cache_dir: str = "stock_cache", max_workers: int = 5, cache_only: bool = False):
        """
        初始化缓存管理器

        Args:
            cache_dir: 缓存目录路径
            max_workers: 并发下载线程数
            cache_only: 是否仅使用缓存模式（不调用API）
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)

        # 创建子目录
        self.data_dir = self.cache_dir / "data"
        self.meta_dir = self.cache_dir / "metadata"
        self.data_dir.mkdir(exist_ok=True)
        self.meta_dir.mkdir(exist_ok=True)

        self.max_workers = max_workers
        self.lock = threading.Lock()
        self.cache_only = cache_only  # 纯缓存模式标志
        
        # 配置日志
        self.logger = logging.getLogger(__name__)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
        
        # 缓存配置
        self.config = {
            "data_expire_days": 7,  # 数据过期天数
            "retry_count": 3,       # 重试次数
            "retry_delay": 2,       # 重试延迟(秒)
            "request_delay": 0.1,   # 请求间隔(秒)
        }
        
        # 加载或创建缓存索引
        self.index_file = self.meta_dir / "cache_index.json"
        self.cache_index = self._load_cache_index()
    
    def _load_cache_index(self) -> Dict:
        """加载缓存索引"""
        if self.index_file.exists():
            try:
                with open(self.index_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.warning(f"加载缓存索引失败: {e}")
        return {}
    
    def _save_cache_index(self):
        """保存缓存索引"""
        try:
            with open(self.index_file, 'w', encoding='utf-8') as f:
                json.dump(self.cache_index, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存缓存索引失败: {e}")
    
    def _get_exchange_code(self, stock_code: str) -> str:
        """获取股票交易所代码"""
        try:
            # 标准化股票代码
            normalized_code = self._normalize_stock_code(stock_code)

            # 根据股票代码判断交易所
            if normalized_code.startswith('60') or normalized_code.startswith('68'):
                # 60开头：上海主板，68开头：科创板
                return "XSHG"
            elif (normalized_code.startswith('00') or
                  normalized_code.startswith('002') or
                  normalized_code.startswith('003') or
                  normalized_code.startswith('30')):
                # 00开头：深圳主板，002/003/30开头：创业板
                return "XSHE"
            else:
                # 默认返回深圳交易所
                return "XSHE"

        except Exception as e:
            self.logger.warning(f"获取股票 {stock_code} 交易所代码失败: {e}")
            return "XSHE"

    def _get_cache_file_path(self, stock_code: str, use_exchange_suffix: bool = True) -> Path:
        """获取股票数据缓存文件路径"""
        # 使用股票代码的前3位作为子目录，避免单个目录文件过多
        prefix = stock_code[:3]
        sub_dir = self.data_dir / prefix
        sub_dir.mkdir(exist_ok=True)

        # 生成文件名
        if use_exchange_suffix:
            exchange_code = self._get_exchange_code(stock_code)
            filename_base = f"{stock_code}_{exchange_code}"
        else:
            filename_base = stock_code

        # 优先使用Parquet格式，如果不可用则使用CSV
        try:
            import pyarrow
            return sub_dir / f"{filename_base}.parquet"
        except ImportError:
            return sub_dir / f"{filename_base}.csv"
    
    def _normalize_stock_code(self, stock_code: str) -> str:
        """标准化股票代码"""
        # 移除后缀，统一格式
        if '.' in stock_code:
            return stock_code.split('.')[0]
        return stock_code
    
    def _is_cache_valid(self, stock_code: str) -> bool:
        """检查缓存是否有效"""
        # 尝试多种格式查找缓存索引
        possible_keys = [
            stock_code,  # 原始格式
            f"{stock_code}.XSHE",  # 添加深交所后缀
            f"{stock_code}.XSHG",  # 添加上交所后缀
            stock_code.split('.')[0] if '.' in stock_code else stock_code  # 标准化格式
        ]

        cache_key = None
        for key in possible_keys:
            if key in self.cache_index:
                cache_key = key
                break

        if cache_key is None:
            return False

        cache_info = self.cache_index[cache_key]
        cache_time = datetime.fromisoformat(cache_info['last_update'])
        expire_time = cache_time + timedelta(days=self.config['data_expire_days'])

        return datetime.now() < expire_time
    
    def _download_stock_data(self, stock_code: str, start_date: str = None, end_date: str = None) -> Optional[pd.DataFrame]:
        """下载单只股票的历史数据"""
        normalized_code = self._normalize_stock_code(stock_code)
        
        if start_date is None:
            start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        for attempt in range(self.config['retry_count']):
            try:
                self.logger.info(f"下载股票 {stock_code} 数据 (尝试 {attempt + 1}/{self.config['retry_count']})")
                
                # 调用AKShare API
                df = ak.stock_zh_a_hist(symbol=normalized_code, period="daily", 
                                      start_date=start_date, end_date=end_date, adjust="")
                
                if df is not None and len(df) > 0:
                    # 标准化列名
                    df = df.rename(columns={
                        "日期": "date",
                        "开盘": "open",
                        "收盘": "close",
                        "最高": "high",
                        "最低": "low",
                        "成交量": "volume",
                        "成交额": "amount",
                        "振幅": "amplitude",
                        "涨跌幅": "pct_chg",
                        "涨跌额": "change",
                        "换手率": "turnover"
                    })
                    
                    # 确保日期格式正确
                    df['date'] = pd.to_datetime(df['date'])
                    
                    # 数据类型转换
                    numeric_columns = ['open', 'close', 'high', 'low', 'volume']
                    for col in numeric_columns:
                        if col in df.columns:
                            df[col] = pd.to_numeric(df[col], errors='coerce')
                    
                    # 添加股票代码列
                    df['stock_code'] = stock_code
                    
                    self.logger.info(f"成功下载股票 {stock_code} 数据，共 {len(df)} 条记录")
                    return df
                else:
                    self.logger.warning(f"股票 {stock_code} 返回空数据")
                    
            except Exception as e:
                self.logger.warning(f"下载股票 {stock_code} 数据失败 (尝试 {attempt + 1}): {e}")
                if attempt < self.config['retry_count'] - 1:
                    time.sleep(self.config['retry_delay'])
        
        self.logger.error(f"股票 {stock_code} 数据下载失败，已达到最大重试次数")
        return None
    
    def get_stock_data(self, stock_code: str, start_date: str = None, end_date: str = None,
                      force_refresh: bool = False) -> Optional[pd.DataFrame]:
        """
        获取股票数据，优先从缓存读取

        Args:
            stock_code: 股票代码
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            force_refresh: 强制刷新缓存

        Returns:
            股票数据DataFrame或None
        """
        # 尝试多种格式查找缓存
        possible_codes = [
            stock_code,  # 原始格式
            f"{stock_code}.XSHE",  # 添加深交所后缀
            f"{stock_code}.XSHG",  # 添加上交所后缀
            stock_code.split('.')[0] if '.' in stock_code else stock_code  # 标准化格式
        ]

        # 查找存在的缓存文件和索引
        cache_file = None
        cache_key = None

        for code in possible_codes:
            # 检查缓存索引
            if code in self.cache_index:
                cache_key = code
                # 使用标准化代码获取文件路径
                normalized_code = self._normalize_stock_code(code)

                # 优先查找新格式文件（包含交易所代码）
                test_cache_file = self._get_cache_file_path(normalized_code, use_exchange_suffix=True)
                if test_cache_file.exists():
                    cache_file = test_cache_file
                    break

                # 如果新格式不存在，查找旧格式文件（兼容性）
                test_cache_file_old = self._get_cache_file_path(normalized_code, use_exchange_suffix=False)
                if test_cache_file_old.exists():
                    cache_file = test_cache_file_old
                    # 找到旧格式文件时，标记需要迁移
                    self.logger.info(f"找到旧格式缓存文件: {test_cache_file_old}")
                    break

        self.logger.debug(f"股票代码: {stock_code}")
        self.logger.debug(f"找到缓存键: {cache_key}")
        self.logger.debug(f"缓存文件路径: {cache_file}")

        # 检查是否需要从缓存读取
        if not force_refresh and cache_file and cache_key and self._is_cache_valid(cache_key):
            try:
                # 根据文件扩展名选择读取方法
                if cache_file.suffix == '.parquet':
                    df = pd.read_parquet(cache_file)
                else:
                    df = pd.read_csv(cache_file)
                    df['date'] = pd.to_datetime(df['date'])

                self.logger.info(f"从缓存读取股票 {stock_code} 数据，共 {len(df)} 条记录")

                # 根据日期范围过滤数据
                if start_date or end_date:
                    original_count = len(df)
                    df = self._filter_by_date_range(df, start_date, end_date)
                    filtered_count = len(df)
                    self.logger.info(f"日期过滤: {original_count} -> {filtered_count} 条记录 (范围: {start_date} 到 {end_date})")

                return df
            except Exception as e:
                self.logger.warning(f"读取缓存文件失败: {e}")

        # 纯缓存模式：不调用API
        if self.cache_only:
            self.logger.warning(f"纯缓存模式：无法获取股票 {stock_code} 的数据，缓存中不存在")
            return None

        # 从API下载数据
        normalized_code = self._normalize_stock_code(stock_code)
        df = self._download_stock_data(stock_code, start_date, end_date)
        if df is not None:
            # 保存到缓存（使用标准化代码）
            self._save_to_cache(normalized_code, df)

        return df
    
    def _filter_by_date_range(self, df: pd.DataFrame, start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """根据日期范围过滤数据"""
        if start_date:
            start_date = pd.to_datetime(start_date)
            df = df[df['date'] >= start_date]
        
        if end_date:
            end_date = pd.to_datetime(end_date)
            df = df[df['date'] <= end_date]
        
        return df
    
    def _save_to_cache(self, stock_code: str, df: pd.DataFrame):
        """保存数据到缓存"""
        try:
            # 使用包含交易所代码的新格式文件名
            cache_file = self._get_cache_file_path(stock_code, use_exchange_suffix=True)

            # 检查是否存在旧格式文件，如果存在则删除（迁移）
            old_cache_file = self._get_cache_file_path(stock_code, use_exchange_suffix=False)
            if old_cache_file.exists() and old_cache_file != cache_file:
                try:
                    old_cache_file.unlink()
                    self.logger.info(f"已删除旧格式缓存文件: {old_cache_file}")
                except Exception as e:
                    self.logger.warning(f"删除旧格式缓存文件失败: {e}")

            # 根据文件扩展名选择保存方法
            if cache_file.suffix == '.parquet':
                df.to_parquet(cache_file, index=False)
            else:
                df.to_csv(cache_file, index=False, encoding='utf-8')

            # 更新缓存索引
            with self.lock:
                self.cache_index[stock_code] = {
                    'last_update': datetime.now().isoformat(),
                    'record_count': len(df),
                    'date_range': {
                        'start': df['date'].min().isoformat(),
                        'end': df['date'].max().isoformat()
                    }
                }
                self._save_cache_index()

            exchange_code = self._get_exchange_code(stock_code)
            self.logger.info(f"股票 {stock_code} ({exchange_code}) 数据已保存到缓存: {cache_file.name}")

        except Exception as e:
            self.logger.error(f"保存缓存失败: {e}")
    
    def batch_download(self, stock_codes: List[str], start_date: str = None, end_date: str = None, 
                      force_refresh: bool = False) -> Dict[str, bool]:
        """
        批量下载股票数据
        
        Args:
            stock_codes: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            force_refresh: 强制刷新缓存
            
        Returns:
            下载结果字典 {stock_code: success}
        """
        results = {}
        
        # 过滤需要下载的股票（使用改进的缓存检查）
        if not force_refresh:
            stock_codes = [code for code in stock_codes if not self._is_cache_valid(code)]
        
        if not stock_codes:
            self.logger.info("所有股票数据都已缓存且有效")
            return {code: True for code in stock_codes}
        
        self.logger.info(f"开始批量下载 {len(stock_codes)} 只股票数据")
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交下载任务
            future_to_code = {
                executor.submit(self.get_stock_data, code, start_date, end_date, force_refresh): code
                for code in stock_codes
            }
            
            # 收集结果
            for future in as_completed(future_to_code):
                stock_code = future_to_code[future]
                try:
                    result = future.result()
                    results[stock_code] = result is not None
                    if result is not None:
                        self.logger.info(f"✅ {stock_code} 下载成功")
                    else:
                        self.logger.error(f"❌ {stock_code} 下载失败")
                except Exception as e:
                    self.logger.error(f"❌ {stock_code} 下载异常: {e}")
                    results[stock_code] = False
                
                # 添加请求间隔
                time.sleep(self.config['request_delay'])
        
        success_count = sum(results.values())
        self.logger.info(f"批量下载完成: {success_count}/{len(stock_codes)} 成功")
        
        return results
    
    def get_cache_status(self) -> Dict:
        """获取缓存状态信息"""
        total_stocks = len(self.cache_index)
        valid_stocks = sum(1 for code in self.cache_index.keys() if self._is_cache_valid(code))
        
        # 计算缓存大小（包括CSV和Parquet文件）
        cache_size = 0
        for file_path in self.data_dir.rglob("*.parquet"):
            cache_size += file_path.stat().st_size
        for file_path in self.data_dir.rglob("*.csv"):
            cache_size += file_path.stat().st_size
        
        return {
            'total_stocks': total_stocks,
            'valid_stocks': valid_stocks,
            'expired_stocks': total_stocks - valid_stocks,
            'cache_size_mb': round(cache_size / (1024 * 1024), 2),
            'cache_dir': str(self.cache_dir),
            'last_update': max([info['last_update'] for info in self.cache_index.values()]) if self.cache_index else None
        }
    
    def clean_expired_cache(self) -> int:
        """清理过期缓存"""
        expired_count = 0
        expired_codes = []

        for stock_code in list(self.cache_index.keys()):
            if not self._is_cache_valid(stock_code):
                expired_codes.append(stock_code)
        
        for stock_code in expired_codes:
            try:
                # 清理新格式文件
                cache_file = self._get_cache_file_path(stock_code, use_exchange_suffix=True)
                if cache_file.exists():
                    cache_file.unlink()
                    self.logger.debug(f"已删除新格式缓存文件: {cache_file}")

                # 清理旧格式文件（如果存在）
                old_cache_file = self._get_cache_file_path(stock_code, use_exchange_suffix=False)
                if old_cache_file.exists():
                    old_cache_file.unlink()
                    self.logger.debug(f"已删除旧格式缓存文件: {old_cache_file}")

                del self.cache_index[stock_code]
                expired_count += 1
                
            except Exception as e:
                self.logger.error(f"清理过期缓存失败 {stock_code}: {e}")
        
        if expired_count > 0:
            self._save_cache_index()
            self.logger.info(f"已清理 {expired_count} 个过期缓存")
        
        return expired_count
