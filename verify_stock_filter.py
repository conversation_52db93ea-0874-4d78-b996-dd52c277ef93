#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证股票代码过滤功能
"""

import sys
import os

def verify_filter_functions():
    """验证过滤函数是否正确添加"""
    print("=== 验证过滤函数 ===")
    
    try:
        # 检查 stock_data_precacher.py 中的函数
        with open('stock_data_precacher.py', 'r', encoding='utf-8') as f:
            precacher_content = f.read()
        
        # 检查 stock_data_cache.py 中的函数
        with open('stock_data_cache.py', 'r', encoding='utf-8') as f:
            cache_content = f.read()
        
        # 检查关键函数和修改
        checks = [
            ("def is_valid_a_stock_code", precacher_content, "股票代码验证函数"),
            ("def filter_a_stock_codes", precacher_content, "股票代码过滤函数"),
            ("filter_a_stock_codes(raw_stock_codes)", precacher_content, "CSV加载过滤调用"),
            ("filter_a_stock_codes(raw_stock_codes)", precacher_content, "AKShare过滤调用"),
            ("def _is_valid_a_stock_code", cache_content, "缓存管理器验证函数"),
            ("if not self._is_valid_a_stock_code", cache_content, "数据获取前验证"),
        ]
        
        all_found = True
        for check_str, content, description in checks:
            if check_str in content:
                print(f"  ✓ 找到 {description}")
            else:
                print(f"  ✗ 未找到 {description}")
                all_found = False
        
        return all_found
        
    except FileNotFoundError as e:
        print(f"  ✗ 文件未找到: {e}")
        return False
    except Exception as e:
        print(f"  ✗ 检查失败: {e}")
        return False

def verify_filter_rules():
    """验证过滤规则"""
    print("\n=== 验证过滤规则 ===")
    
    # 模拟验证函数
    def mock_is_valid_a_stock_code(stock_code: str) -> bool:
        """模拟股票代码验证"""
        if not stock_code or len(stock_code) < 6:
            return False
        
        # 标准化股票代码
        if '.' in stock_code:
            code = stock_code.split('.')[0]
        else:
            code = stock_code
        
        # 确保是6位数字
        if not code.isdigit() or len(code) != 6:
            return False
        
        # 有效前缀
        valid_prefixes = ['00', '002', '003', '30', '60', '68']
        # 无效前缀
        invalid_prefixes = ['4', '8', '9']
        
        # 检查无效前缀
        for prefix in invalid_prefixes:
            if code.startswith(prefix):
                return False
        
        # 检查有效前缀
        for prefix in valid_prefixes:
            if code.startswith(prefix):
                return True
        
        return False
    
    # 测试用例
    test_cases = [
        # 有效A股代码
        ("000001", True, "深圳主板"),
        ("000002", True, "深圳主板"),
        ("002001", True, "创业板"),
        ("003001", True, "创业板"),
        ("300001", True, "创业板"),
        ("600001", True, "上海主板"),
        ("688001", True, "科创板"),
        
        # 应被过滤的代码
        ("400001", False, "新三板"),
        ("430001", False, "北交所"),
        ("800001", False, "其他8开头"),
        ("900001", False, "B股"),
        
        # 格式无效
        ("12345", False, "长度不足"),
        ("abcdef", False, "非数字"),
    ]
    
    all_passed = True
    for code, expected, description in test_cases:
        actual = mock_is_valid_a_stock_code(code)
        status = "✓" if actual == expected else "✗"
        print(f"  {status} {code}: {actual} (期望: {expected}) - {description}")
        if actual != expected:
            all_passed = False
    
    return all_passed

def verify_logging_enhancements():
    """验证日志增强"""
    print("\n=== 验证日志增强 ===")
    
    try:
        with open('stock_data_precacher.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查日志相关的修改
        log_checks = [
            ("原始加载了", "原始数量日志"),
            ("股票代码过滤结果", "过滤结果日志"),
            ("过滤详情", "详细过滤信息"),
            ("新三板(4开头)", "新三板过滤统计"),
            ("北交所等(8开头)", "北交所过滤统计"),
            ("B股等(9开头)", "B股过滤统计"),
            ("格式无效", "格式错误统计"),
        ]
        
        all_found = True
        for check_str, description in log_checks:
            if check_str in content:
                print(f"  ✓ 找到 {description}")
            else:
                print(f"  ✗ 未找到 {description}")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"  ✗ 检查失败: {e}")
        return False

def verify_exchange_code_handling():
    """验证交易所代码处理"""
    print("\n=== 验证交易所代码处理 ===")
    
    try:
        with open('stock_data_cache.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查交易所代码相关修改
        exchange_checks = [
            ("if not self._is_valid_a_stock_code", "获取交易所前验证"),
            ('return "UNKNOWN"', "无效代码返回UNKNOWN"),
            ("跳过无效的股票代码", "数据获取时跳过无效代码"),
        ]
        
        all_found = True
        for check_str, description in exchange_checks:
            if check_str in content:
                print(f"  ✓ 找到 {description}")
            else:
                print(f"  ✗ 未找到 {description}")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"  ✗ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("股票代码过滤功能验证")
    print("=" * 50)
    
    results = []
    
    # 执行各项验证
    results.append(("过滤函数检查", verify_filter_functions()))
    results.append(("过滤规则验证", verify_filter_rules()))
    results.append(("日志增强验证", verify_logging_enhancements()))
    results.append(("交易所代码处理", verify_exchange_code_handling()))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("验证结果汇总:")
    
    all_passed = True
    for test_name, passed in results:
        status = "✓ 通过" if passed else "✗ 失败"
        print(f"  {status} {test_name}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有验证都通过了！")
        print("\n修改总结:")
        print("1. ✅ 添加了股票代码验证函数")
        print("2. ✅ 实现了股票代码过滤逻辑")
        print("3. ✅ 修改了数据加载函数")
        print("4. ✅ 增强了缓存管理器验证")
        print("5. ✅ 添加了详细的日志记录")
        
        print("\n过滤规则:")
        print("✅ 保留: 00/002/003/30/60/68 开头的A股代码")
        print("❌ 过滤: 4/8/9 开头的非A股代码")
        
        print("\n功能特点:")
        print("- 多层次验证（输入、API、处理）")
        print("- 详细的过滤统计信息")
        print("- 兼容现有交易所命名格式")
        print("- 完整的错误处理和日志")
        
        print("\n下一步:")
        print("- 可以运行 python stock_data_precacher.py 测试过滤功能")
        print("- 观察日志中的过滤统计信息")
        print("- 验证只处理有效的A股代码")
    else:
        print("❌ 部分验证失败，请检查修改内容")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
